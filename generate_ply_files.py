#!/usr/bin/env python3
"""
生成waypoint_example所需的PLY文件
用法: 
    python3 generate_ply_files.py                    # 生成示例文件
    python3 generate_ply_files.py waypoints.txt      # 从txt文件生成航点
    python3 generate_ply_files.py waypoints.txt boundary.txt  # 从txt文件生成航点和边界
"""

import sys
import os

def write_ply_file(filename, points, properties=['x', 'y', 'z']):
    """
    写入PLY格式文件
    Args:
        filename: 输出文件名
        points: 点坐标列表 [(x1,y1,z1), (x2,y2,z2), ...]
        properties: 属性列表，默认为['x', 'y', 'z']
    """
    with open(filename, 'w') as f:
        # 写入头部
        f.write("ply\n")
        f.write("format ascii 1.0\n")
        f.write(f"element vertex {len(points)}\n")
        
        # 写入属性定义
        for prop in properties:
            f.write(f"property float {prop}\n")
        
        f.write("end_header\n")
        
        # 写入点数据
        for point in points:
            if len(point) >= len(properties):
                line = '\t'.join([str(point[i]) for i in range(len(properties))])
                f.write(f"{line}\n")
    
    print(f"已生成: {filename} ({len(points)}个点)")

def generate_waypoints_ply():
    """生成自定义航点文件"""
    # 示例：定义你的自定义航点 (x, y, z)
    custom_waypoints = [
        (0.0, 0.0, 0.5),      # 起点
        (5.0, 0.0, 0.5),      # 向前5米
        (5.0, 5.0, 0.5),      # 右转5米
        (10.0, 5.0, 1.0),     # 向前5米，高度1米
        (10.0, 10.0, 1.0),    # 右转5米
        (0.0, 10.0, 0.5),     # 向左10米
        (0.0, 0.0, 0.5),      # 回到起点
    ]
    
    write_ply_file("waypoints_custom.ply", custom_waypoints)
    return custom_waypoints

def generate_boundary_ply():
    """生成自定义边界文件"""
    # 示例：定义导航区域边界 (形成闭合多边形)
    custom_boundary = [
        (-2.0, -2.0, 0.0),    # 左下角
        (-2.0, 12.0, 0.0),    # 左上角  
        (12.0, 12.0, 0.0),    # 右上角
        (12.0, -2.0, 0.0),    # 右下角
        (-2.0, -2.0, 0.0),    # 闭合到起点
    ]
    
    write_ply_file("boundary_custom.ply", custom_boundary)
    return custom_boundary

def read_points_from_txt(filename):
    """
    从txt文件读取点坐标
    支持格式:
        x y z           # 空格分隔
        x,y,z           # 逗号分隔  
        x	y	z       # Tab分隔
    """
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return []
    
    points = []
    try:
        with open(filename, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                
                # 跳过空行和注释行
                if not line or line.startswith('#'):
                    continue
                
                # 尝试不同的分隔符
                coords = None
                if ',' in line:
                    coords = line.split(',')
                elif '\t' in line:
                    coords = line.split('\t')
                else:
                    coords = line.split()
                
                # 解析坐标
                if len(coords) >= 2:
                    try:
                        x = float(coords[0].strip())
                        y = float(coords[1].strip())
                        z = float(coords[2].strip()) if len(coords) > 2 else 0.0
                        points.append((x, y, z))
                    except ValueError:
                        print(f"警告: 第{line_num}行格式错误: {line}")
                        continue
                else:
                    print(f"警告: 第{line_num}行坐标不足: {line}")
                    
        print(f"从 {filename} 读取了 {len(points)} 个点")
        return points
        
    except Exception as e:
        print(f"错误: 读取文件 {filename} 失败: {e}")
        return []

def generate_from_txt_files(waypoints_file, boundary_file=None):
    """
    从txt文件生成PLY文件
    Args:
        waypoints_file: 航点坐标txt文件
        boundary_file: 边界坐标txt文件(可选)
    """
    print(f"\n=== 从txt文件生成PLY ===")
    
    # 读取航点
    waypoints = read_points_from_txt(waypoints_file)
    if not waypoints:
        print("错误: 无法读取航点数据")
        return False
    
    # 生成航点PLY文件
    waypoints_ply = waypoints_file.replace('.txt', '.ply')
    write_ply_file(waypoints_ply, waypoints)
    
    # 读取边界(如果提供)
    if boundary_file:
        boundary = read_points_from_txt(boundary_file)
        if boundary:
            # 确保边界闭合
            if len(boundary) > 1 and boundary[0] != boundary[-1]:
                boundary.append(boundary[0])
                print("自动闭合边界多边形")
            
            boundary_ply = boundary_file.replace('.txt', '.ply')
            write_ply_file(boundary_ply, boundary)
        else:
            print("警告: 无法读取边界数据")
    
    return True

def generate_from_coordinates(waypoint_coords, boundary_coords):
    """
    根据给定坐标生成PLY文件
    Args:
        waypoint_coords: 航点坐标列表 [(x,y,z), ...]
        boundary_coords: 边界坐标列表 [(x,y,z), ...]
    """
    write_ply_file("waypoints_custom.ply", waypoint_coords)
    write_ply_file("boundary_custom.ply", boundary_coords)

def create_example_txt_files():
    """创建示例txt文件"""
    # 创建示例航点文件
    waypoints_txt = """# 航点坐标文件 (x, y, z)
# 支持格式: 空格分隔、逗号分隔、Tab分隔
0.0 0.0 0.8
5.0 0.0 0.8  
5.0 5.0 0.5
10.0 5.0 1.0
10.0 10.0 1.0
0.0 10.0 0.5
0.0 0.0 0.8"""

    # 创建示例边界文件  
    boundary_txt = """# 边界坐标文件 (形成闭合多边形)
-2.0, -2.0, 0.0
-2.0, 12.0, 0.0
12.0, 12.0, 0.0
12.0, -2.0, 0.0"""

    with open("waypoints_example.txt", 'w') as f:
        f.write(waypoints_txt)
    
    with open("boundary_example.txt", 'w') as f:
        f.write(boundary_txt)
    
    print("已创建示例txt文件:")
    print("  - waypoints_example.txt")
    print("  - boundary_example.txt")

if __name__ == "__main__":
    print("=== PLY文件生成工具 ===")
    
    # 检查命令行参数
    if len(sys.argv) == 1:
        # 无参数 - 生成示例文件和创建示例txt
        print("\n1. 生成示例PLY文件:")
        waypoints = generate_waypoints_ply()
        boundary = generate_boundary_ply()
        
        print("\n2. 创建示例txt文件:")
        create_example_txt_files()
        
        print("\n3. 自定义坐标示例:")
        my_waypoints = [
            (0, 0, 0.8),
            (3, 2, 0.8), 
            (6, 0, 1.2),
            (9, 3, 1.0),
            (12, 1, 0.8)
        ]
        
        my_boundary = [
            (-1, -1, 0),
            (-1, 5, 0),
            (14, 5, 0), 
            (14, -1, 0),
            (-1, -1, 0)
        ]
        
        generate_from_coordinates(my_waypoints, my_boundary)
        
    elif len(sys.argv) == 2:
        # 一个参数 - 从txt文件生成航点PLY
        waypoints_file = sys.argv[1]
        if generate_from_txt_files(waypoints_file):
            print(f"\n成功从 {waypoints_file} 生成PLY文件")
        else:
            print(f"\n生成失败")
            
    elif len(sys.argv) == 3:
        # 两个参数 - 从txt文件生成航点和边界PLY
        waypoints_file = sys.argv[1]
        boundary_file = sys.argv[2]
        if generate_from_txt_files(waypoints_file, boundary_file):
            print(f"\n成功从 {waypoints_file} 和 {boundary_file} 生成PLY文件")
        else:
            print(f"\n生成失败")
            
    else:
        print("\n用法:")
        print("  python3 generate_ply_files.py                    # 生成示例文件")
        print("  python3 generate_ply_files.py waypoints.txt      # 从txt生成航点PLY")
        print("  python3 generate_ply_files.py waypoints.txt boundary.txt  # 从txt生成航点和边界PLY")
        sys.exit(1)
    
    print("\n=== 使用说明 ===")
    print("TXT文件格式支持:")
    print("  - 空格分隔: x y z")
    print("  - 逗号分隔: x,y,z") 
    print("  - Tab分隔: x	y	z")
    print("  - 支持注释行 (以#开头)")
    print("  - 如果只有x,y坐标，z默认为0.0")
    print("\n部署步骤:")
    print("1. 将生成的.ply文件复制到 src/waypoint_example/data/ 目录")
    print("2. 更新launch文件中的文件路径参数")
    print("3. 重新编译: colcon build --packages-select waypoint_example")
    print("4. 启动: ros2 launch waypoint_example waypoint_example_garage.launch")