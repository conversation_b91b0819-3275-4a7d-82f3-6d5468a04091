#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from nav_msgs.msg import Path
from geometry_msgs.msg import PoseStamped
import math

class PathPublisher(Node):
    def __init__(self):
        super().__init__('test_path_publisher')
        self.publisher = self.create_publisher(Path, '/path', 10)
        self.timer = self.create_timer(3.0, self.publish_test_path)  # 每3秒发布一次
        self.get_logger().info('Test path publisher started')

    def publish_test_path(self):
        path_msg = Path()
        path_msg.header.stamp = self.get_clock().now().to_msg()
        path_msg.header.frame_id = "map"
        
        # 创建一个简单的测试路径（直线）
        num_points = 10
        for i in range(num_points):
            pose = PoseStamped()
            pose.header.stamp = path_msg.header.stamp
            pose.header.frame_id = "map"
            
            # 创建一条从(0,0)到(10,0)的直线路径
            pose.pose.position.x = float(i)
            pose.pose.position.y = 0.0
            pose.pose.position.z = 0.0
            
            # 简单的朝向（朝前）
            pose.pose.orientation.x = 0.0
            pose.pose.orientation.y = 0.0
            pose.pose.orientation.z = 0.0
            pose.pose.orientation.w = 1.0
            
            path_msg.poses.append(pose)
        
        self.publisher.publish(path_msg)
        self.get_logger().info(f'Published test path with {len(path_msg.poses)} points')

def main(args=None):
    rclpy.init(args=args)
    node = PathPublisher()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    
    node.destroy_node()
    rclpy.shutdown()

if __name__ == '__main__':
    main()
