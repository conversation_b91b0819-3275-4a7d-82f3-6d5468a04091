[0.066s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.066s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7696f4f390f0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7696f4f38ca0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7696f4f38ca0>>)
[0.179s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.179s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.179s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.179s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.179s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.179s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.179s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/saoxueche0808pm/saoxueche'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['ignore', 'ignore_ament_install']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'ignore'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'ignore_ament_install'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['colcon_pkg']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'colcon_pkg'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['colcon_meta']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'colcon_meta'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['ros']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'ros'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['cmake', 'python']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'cmake'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'python'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['python_setup_py']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'python_setup_py'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['ignore', 'ignore_ament_install']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ignore'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ignore_ament_install'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['colcon_pkg']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'colcon_pkg'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['colcon_meta']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'colcon_meta'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['ros']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ros'
[0.190s] DEBUG:colcon.colcon_core.package_identification:Package 'src/integrated_navigation' with type 'ros.ament_cmake' and name 'integrated_navigation'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['ignore', 'ignore_ament_install']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ignore'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ignore_ament_install'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['colcon_pkg']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'colcon_pkg'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['colcon_meta']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'colcon_meta'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['ros']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ros'
[0.191s] DEBUG:colcon.colcon_core.package_identification:Package 'src/loam_interface' with type 'ros.ament_cmake' and name 'loam_interface'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ignore', 'ignore_ament_install']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore_ament_install'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_pkg']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_pkg'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_meta']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_meta'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ros']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ros'
[0.192s] DEBUG:colcon.colcon_core.package_identification:Package 'src/local_planner' with type 'ros.ament_cmake' and name 'local_planner'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['ignore', 'ignore_ament_install']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ignore'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ignore_ament_install'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['colcon_pkg']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'colcon_pkg'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['colcon_meta']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'colcon_meta'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['ros']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ros'
[0.194s] DEBUG:colcon.colcon_core.package_identification:Package 'src/nav2_smac_test' with type 'ros.ament_cmake' and name 'nav2_smac_test'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['ignore', 'ignore_ament_install']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ignore'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ignore_ament_install'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['colcon_pkg']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'colcon_pkg'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['colcon_meta']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'colcon_meta'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['ros']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ros'
[0.194s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sensor_scan_generation' with type 'ros.ament_cmake' and name 'sensor_scan_generation'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ignore', 'ignore_ament_install']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore_ament_install'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_pkg']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_pkg'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_meta']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_meta'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ros']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ros'
[0.195s] DEBUG:colcon.colcon_core.package_identification:Package 'src/terrain_analysis' with type 'ros.ament_cmake' and name 'terrain_analysis'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['ignore', 'ignore_ament_install']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ignore'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ignore_ament_install'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['colcon_pkg']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'colcon_pkg'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['colcon_meta']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'colcon_meta'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['ros']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ros'
[0.196s] DEBUG:colcon.colcon_core.package_identification:Package 'src/terrain_analysis_ext' with type 'ros.ament_cmake' and name 'terrain_analysis_ext'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) ignored
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) ignored
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ignore_ament_install'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['colcon_pkg']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'colcon_pkg'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['colcon_meta']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'colcon_meta'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['ros']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ros'
[0.198s] DEBUG:colcon.colcon_core.package_identification:Package 'src/visualization_tools' with type 'ros.ament_cmake' and name 'visualization_tools'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['ignore', 'ignore_ament_install']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ignore_ament_install'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['colcon_pkg']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'colcon_pkg'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['colcon_meta']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'colcon_meta'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['ros']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ros'
[0.199s] DEBUG:colcon.colcon_core.package_identification:Package 'src/waypoint_example' with type 'ros.ament_cmake' and name 'waypoint_example'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['ignore', 'ignore_ament_install']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ignore'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ignore_ament_install'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['colcon_pkg']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'colcon_pkg'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['colcon_meta']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'colcon_meta'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['ros']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ros'
[0.200s] DEBUG:colcon.colcon_core.package_identification:Package 'src/waypoint_rviz_plugin' with type 'ros.ament_cmake' and name 'waypoint_rviz_plugin'
[0.200s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.200s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.200s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.200s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.200s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.219s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.219s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.225s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 10 installed packages in /home/<USER>/saoxueche0808pm/saoxueche/install
[0.225s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 14 installed packages in /home/<USER>/saoxueche/install
[0.226s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 473 installed packages in /opt/ros/humble
[0.228s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.262s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_args' from command line to 'None'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_target' from command line to 'None'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_clean_cache' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_clean_first' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_force_configure' from command line to 'False'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'ament_cmake_args' from command line to 'None'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'catkin_cmake_args' from command line to 'None'
[0.262s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.262s] DEBUG:colcon.colcon_core.verb:Building package 'integrated_navigation' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/integrated_navigation', 'symlink_install': False, 'test_result_base': None}
[0.263s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_target' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_clean_cache' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_clean_first' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_force_configure' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'ament_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'catkin_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.263s] DEBUG:colcon.colcon_core.verb:Building package 'loam_interface' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/loam_interface', 'symlink_install': False, 'test_result_base': None}
[0.263s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.263s] DEBUG:colcon.colcon_core.verb:Building package 'local_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner', 'symlink_install': False, 'test_result_base': None}
[0.263s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_target' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_clean_cache' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_clean_first' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_force_configure' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'ament_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'catkin_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.263s] DEBUG:colcon.colcon_core.verb:Building package 'nav2_smac_test' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/nav2_smac_test', 'symlink_install': False, 'test_result_base': None}
[0.264s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_target' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_clean_cache' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_clean_first' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_force_configure' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'ament_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'catkin_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.264s] DEBUG:colcon.colcon_core.verb:Building package 'sensor_scan_generation' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/sensor_scan_generation', 'symlink_install': False, 'test_result_base': None}
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_target' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_clean_cache' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_clean_first' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_force_configure' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'ament_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'catkin_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.264s] DEBUG:colcon.colcon_core.verb:Building package 'terrain_analysis' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis', 'symlink_install': False, 'test_result_base': None}
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_target' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_clean_cache' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_clean_first' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_force_configure' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'ament_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'catkin_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.264s] DEBUG:colcon.colcon_core.verb:Building package 'terrain_analysis_ext' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis_ext', 'symlink_install': False, 'test_result_base': None}
[0.264s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_target' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_clean_cache' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_clean_first' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_force_configure' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'ament_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'catkin_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.265s] DEBUG:colcon.colcon_core.verb:Building package 'visualization_tools' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/visualization_tools', 'symlink_install': False, 'test_result_base': None}
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_target' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_clean_cache' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_clean_first' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_force_configure' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'ament_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'catkin_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.265s] DEBUG:colcon.colcon_core.verb:Building package 'waypoint_example' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_example', 'symlink_install': False, 'test_result_base': None}
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_target' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_clean_cache' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_clean_first' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_force_configure' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'ament_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'catkin_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.265s] DEBUG:colcon.colcon_core.verb:Building package 'waypoint_rviz_plugin' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_rviz_plugin', 'symlink_install': False, 'test_result_base': None}
[0.265s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.266s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.267s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/integrated_navigation' with build type 'ament_cmake'
[0.267s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/integrated_navigation'
[0.268s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.268s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.268s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.271s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/loam_interface' with build type 'ament_cmake'
[0.271s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/loam_interface'
[0.272s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.272s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.275s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner' with build type 'ament_cmake'
[0.275s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner'
[0.275s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.275s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.278s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/nav2_smac_test' with build type 'ament_cmake'
[0.278s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/nav2_smac_test'
[0.278s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.278s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.281s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/sensor_scan_generation' with build type 'ament_cmake'
[0.281s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/sensor_scan_generation'
[0.281s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.281s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.284s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis' with build type 'ament_cmake'
[0.284s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis'
[0.284s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.284s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.287s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis_ext' with build type 'ament_cmake'
[0.287s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis_ext'
[0.287s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.287s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.290s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/visualization_tools' with build type 'ament_cmake'
[0.290s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/visualization_tools'
[0.290s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.290s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.293s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_example' with build type 'ament_cmake'
[0.293s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_example'
[0.293s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.293s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.296s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_rviz_plugin' with build type 'ament_cmake'
[0.296s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_rviz_plugin'
[0.297s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.297s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.304s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation -- -j16 -l16
[0.306s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface -- -j16 -l16
[0.308s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner -- -j16 -l16
[0.310s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test -- -j16 -l16
[0.313s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation -- -j16 -l16
[0.315s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis -- -j16 -l16
[0.319s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext -- -j16 -l16
[0.323s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools -- -j16 -l16
[0.328s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example -- -j16 -l16
[0.335s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin -- -j16 -l16
[0.339s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation -- -j16 -l16
[0.348s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation
[0.349s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test -- -j16 -l16
[0.351s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test
[0.358s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(integrated_navigation)
[0.359s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation
[0.364s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation' for CMake module files
[0.367s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation' for CMake config files
[0.367s] Level 1:colcon.colcon_core.shell:create_environment_hook('integrated_navigation', 'cmake_prefix_path')
[0.367s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.ps1'
[0.368s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.dsv'
[0.369s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.sh'
[0.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/bin'
[0.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/lib/pkgconfig/integrated_navigation.pc'
[0.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/lib/python3.10/site-packages'
[0.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/bin'
[0.371s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.ps1'
[0.372s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.dsv'
[0.373s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.sh'
[0.374s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.bash'
[0.375s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.zsh'
[0.376s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/colcon-core/packages/integrated_navigation)
[0.377s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(integrated_navigation)
[0.377s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation' for CMake module files
[0.377s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation' for CMake config files
[0.378s] Level 1:colcon.colcon_core.shell:create_environment_hook('integrated_navigation', 'cmake_prefix_path')
[0.379s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.ps1'
[0.379s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.dsv'
[0.380s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.sh'
[0.380s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/bin'
[0.380s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/lib/pkgconfig/integrated_navigation.pc'
[0.381s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/lib/python3.10/site-packages'
[0.381s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/bin'
[0.381s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.ps1'
[0.383s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.dsv'
[0.383s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.sh'
[0.383s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.bash'
[0.383s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.zsh'
[0.384s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/colcon-core/packages/integrated_navigation)
[0.385s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(nav2_smac_test)
[0.385s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test' for CMake module files
[0.386s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test
[0.386s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test' for CMake config files
[0.386s] Level 1:colcon.colcon_core.shell:create_environment_hook('nav2_smac_test', 'cmake_prefix_path')
[0.386s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.ps1'
[0.387s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.dsv'
[0.387s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.sh'
[0.388s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/bin'
[0.388s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/lib/pkgconfig/nav2_smac_test.pc'
[0.388s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/lib/python3.10/site-packages'
[0.388s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/bin'
[0.388s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.ps1'
[0.389s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.dsv'
[0.389s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.sh'
[0.389s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.bash'
[0.389s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.zsh'
[0.390s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/colcon-core/packages/nav2_smac_test)
[0.390s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(nav2_smac_test)
[0.390s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test' for CMake module files
[0.391s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test' for CMake config files
[0.391s] Level 1:colcon.colcon_core.shell:create_environment_hook('nav2_smac_test', 'cmake_prefix_path')
[0.391s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.ps1'
[0.391s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.dsv'
[0.391s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.sh'
[0.392s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/bin'
[0.392s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/lib/pkgconfig/nav2_smac_test.pc'
[0.392s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/lib/python3.10/site-packages'
[0.392s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/bin'
[0.392s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.ps1'
[0.393s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.dsv'
[0.393s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.sh'
[0.393s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.bash'
[0.394s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.zsh'
[0.394s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/colcon-core/packages/nav2_smac_test)
[0.396s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis -- -j16 -l16
[0.398s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis
[0.398s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner -- -j16 -l16
[0.400s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner
[0.400s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext -- -j16 -l16
[0.402s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext
[0.402s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation -- -j16 -l16
[0.404s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation
[0.405s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface -- -j16 -l16
[0.410s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis)
[0.410s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis' for CMake module files
[0.411s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface
[0.412s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example -- -j16 -l16
[0.413s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example
[0.414s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis
[0.415s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis' for CMake config files
[0.415s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis', 'cmake_prefix_path')
[0.416s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.ps1'
[0.416s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.dsv'
[0.417s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.sh'
[0.417s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib'
[0.417s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/bin'
[0.417s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib/pkgconfig/terrain_analysis.pc'
[0.417s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib/python3.10/site-packages'
[0.417s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/bin'
[0.418s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.ps1'
[0.418s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.dsv'
[0.418s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.sh'
[0.418s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.bash'
[0.418s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.zsh'
[0.419s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/colcon-core/packages/terrain_analysis)
[0.419s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis)
[0.419s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis' for CMake module files
[0.419s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis' for CMake config files
[0.419s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis', 'cmake_prefix_path')
[0.420s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.ps1'
[0.420s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.dsv'
[0.420s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.sh'
[0.420s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib'
[0.420s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/bin'
[0.420s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib/pkgconfig/terrain_analysis.pc'
[0.421s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib/python3.10/site-packages'
[0.421s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/bin'
[0.421s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.ps1'
[0.421s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.dsv'
[0.421s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.sh'
[0.422s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.bash'
[0.422s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.zsh'
[0.422s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/colcon-core/packages/terrain_analysis)
[0.422s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[0.422s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake module files
[0.423s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner
[0.423s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake config files
[0.423s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[0.423s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[0.423s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[0.425s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[0.425s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib'
[0.425s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[0.426s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/pkgconfig/local_planner.pc'
[0.426s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/python3.10/site-packages'
[0.426s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[0.426s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.ps1'
[0.426s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv'
[0.427s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.sh'
[0.427s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.bash'
[0.427s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.zsh'
[0.427s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/colcon-core/packages/local_planner)
[0.427s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[0.428s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake module files
[0.428s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake config files
[0.428s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[0.428s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[0.428s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[0.428s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[0.429s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib'
[0.429s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[0.429s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/pkgconfig/local_planner.pc'
[0.429s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/python3.10/site-packages'
[0.429s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[0.429s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.ps1'
[0.429s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv'
[0.430s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.sh'
[0.430s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.bash'
[0.430s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.zsh'
[0.430s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/colcon-core/packages/local_planner)
[0.432s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sensor_scan_generation)
[0.432s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation' for CMake module files
[0.432s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation
[0.432s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation' for CMake config files
[0.433s] Level 1:colcon.colcon_core.shell:create_environment_hook('sensor_scan_generation', 'cmake_prefix_path')
[0.433s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.ps1'
[0.433s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.dsv'
[0.433s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.sh'
[0.433s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib'
[0.433s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/bin'
[0.433s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib/pkgconfig/sensor_scan_generation.pc'
[0.434s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib/python3.10/site-packages'
[0.434s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/bin'
[0.434s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.ps1'
[0.434s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv'
[0.434s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.sh'
[0.435s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.bash'
[0.435s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.zsh'
[0.435s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/colcon-core/packages/sensor_scan_generation)
[0.435s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sensor_scan_generation)
[0.435s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation' for CMake module files
[0.436s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation' for CMake config files
[0.436s] Level 1:colcon.colcon_core.shell:create_environment_hook('sensor_scan_generation', 'cmake_prefix_path')
[0.436s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.ps1'
[0.436s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.dsv'
[0.436s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.sh'
[0.436s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib'
[0.436s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/bin'
[0.436s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib/pkgconfig/sensor_scan_generation.pc'
[0.437s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib/python3.10/site-packages'
[0.437s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/bin'
[0.437s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.ps1'
[0.437s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv'
[0.437s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.sh'
[0.438s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.bash'
[0.438s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.zsh'
[0.438s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/colcon-core/packages/sensor_scan_generation)
[0.438s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis_ext)
[0.438s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext' for CMake module files
[0.439s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext
[0.439s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext' for CMake config files
[0.439s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis_ext', 'cmake_prefix_path')
[0.439s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.ps1'
[0.440s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.dsv'
[0.440s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.sh'
[0.440s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib'
[0.440s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/bin'
[0.440s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib/pkgconfig/terrain_analysis_ext.pc'
[0.440s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib/python3.10/site-packages'
[0.440s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/bin'
[0.441s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.ps1'
[0.441s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.dsv'
[0.441s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.sh'
[0.441s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.bash'
[0.442s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.zsh'
[0.442s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/colcon-core/packages/terrain_analysis_ext)
[0.442s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis_ext)
[0.442s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext' for CMake module files
[0.442s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext' for CMake config files
[0.442s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis_ext', 'cmake_prefix_path')
[0.443s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.ps1'
[0.443s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.dsv'
[0.443s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.sh'
[0.444s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib'
[0.444s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/bin'
[0.444s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib/pkgconfig/terrain_analysis_ext.pc'
[0.444s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib/python3.10/site-packages'
[0.444s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/bin'
[0.444s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.ps1'
[0.444s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.dsv'
[0.445s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.sh'
[0.445s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.bash'
[0.445s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.zsh'
[0.445s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/colcon-core/packages/terrain_analysis_ext)
[0.446s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(loam_interface)
[0.446s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface' for CMake module files
[0.446s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface' for CMake config files
[0.447s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface
[0.447s] Level 1:colcon.colcon_core.shell:create_environment_hook('loam_interface', 'cmake_prefix_path')
[0.447s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.ps1'
[0.447s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.dsv'
[0.447s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.sh'
[0.448s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib'
[0.448s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/bin'
[0.448s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib/pkgconfig/loam_interface.pc'
[0.448s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib/python3.10/site-packages'
[0.448s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/bin'
[0.448s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.ps1'
[0.448s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.dsv'
[0.449s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.sh'
[0.449s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.bash'
[0.449s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.zsh'
[0.449s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/colcon-core/packages/loam_interface)
[0.450s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(loam_interface)
[0.450s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface' for CMake module files
[0.450s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface' for CMake config files
[0.450s] Level 1:colcon.colcon_core.shell:create_environment_hook('loam_interface', 'cmake_prefix_path')
[0.450s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.ps1'
[0.450s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.dsv'
[0.450s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.sh'
[0.451s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib'
[0.451s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/bin'
[0.451s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib/pkgconfig/loam_interface.pc'
[0.451s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib/python3.10/site-packages'
[0.451s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/bin'
[0.451s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.ps1'
[0.451s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.dsv'
[0.452s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.sh'
[0.452s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.bash'
[0.452s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.zsh'
[0.452s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/colcon-core/packages/loam_interface)
[0.454s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_example)
[0.454s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example' for CMake module files
[0.454s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin -- -j16 -l16
[0.454s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin
[0.454s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example
[0.455s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example' for CMake config files
[0.455s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_example', 'cmake_prefix_path')
[0.455s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.ps1'
[0.455s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.dsv'
[0.455s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.sh'
[0.456s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib'
[0.456s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/bin'
[0.456s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib/pkgconfig/waypoint_example.pc'
[0.456s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib/python3.10/site-packages'
[0.456s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/bin'
[0.456s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.ps1'
[0.456s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.dsv'
[0.457s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.sh'
[0.457s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.bash'
[0.457s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.zsh'
[0.457s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/colcon-core/packages/waypoint_example)
[0.457s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_example)
[0.458s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example' for CMake module files
[0.458s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example' for CMake config files
[0.458s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_example', 'cmake_prefix_path')
[0.458s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.ps1'
[0.458s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.dsv'
[0.458s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.sh'
[0.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib'
[0.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/bin'
[0.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib/pkgconfig/waypoint_example.pc'
[0.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib/python3.10/site-packages'
[0.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/bin'
[0.459s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.ps1'
[0.459s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.dsv'
[0.460s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.sh'
[0.460s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.bash'
[0.460s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.zsh'
[0.460s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/colcon-core/packages/waypoint_example)
[0.463s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_rviz_plugin)
[0.464s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin' for CMake module files
[0.464s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin
[0.464s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin' for CMake config files
[0.464s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_rviz_plugin', 'cmake_prefix_path')
[0.464s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.ps1'
[0.465s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.dsv'
[0.465s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.sh'
[0.465s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib'
[0.465s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/bin'
[0.465s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib/pkgconfig/waypoint_rviz_plugin.pc'
[0.466s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib/python3.10/site-packages'
[0.466s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/bin'
[0.466s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.ps1'
[0.466s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.dsv'
[0.466s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.sh'
[0.467s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.bash'
[0.467s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.zsh'
[0.467s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/colcon-core/packages/waypoint_rviz_plugin)
[0.467s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_rviz_plugin)
[0.467s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin' for CMake module files
[0.468s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin' for CMake config files
[0.468s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_rviz_plugin', 'cmake_prefix_path')
[0.468s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.ps1'
[0.468s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.dsv'
[0.468s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.sh'
[0.469s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib'
[0.469s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/bin'
[0.469s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib/pkgconfig/waypoint_rviz_plugin.pc'
[0.469s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib/python3.10/site-packages'
[0.469s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/bin'
[0.469s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.ps1'
[0.469s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.dsv'
[0.470s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.sh'
[0.470s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.bash'
[0.470s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.zsh'
[0.470s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/colcon-core/packages/waypoint_rviz_plugin)
[0.571s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools -- -j16 -l16
[0.573s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools
[0.605s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(visualization_tools)
[0.605s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools' for CMake module files
[0.606s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools
[0.606s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools' for CMake config files
[0.606s] Level 1:colcon.colcon_core.shell:create_environment_hook('visualization_tools', 'cmake_prefix_path')
[0.607s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.ps1'
[0.607s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.dsv'
[0.607s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.sh'
[0.608s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib'
[0.608s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/bin'
[0.608s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/pkgconfig/visualization_tools.pc'
[0.608s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/python3.10/site-packages'
[0.608s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/bin'
[0.608s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.ps1'
[0.608s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.dsv'
[0.609s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.sh'
[0.609s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.bash'
[0.609s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.zsh'
[0.609s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/colcon-core/packages/visualization_tools)
[0.610s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(visualization_tools)
[0.610s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools' for CMake module files
[0.610s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools' for CMake config files
[0.610s] Level 1:colcon.colcon_core.shell:create_environment_hook('visualization_tools', 'cmake_prefix_path')
[0.610s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.ps1'
[0.610s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.dsv'
[0.611s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.sh'
[0.611s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib'
[0.611s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/bin'
[0.611s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/pkgconfig/visualization_tools.pc'
[0.611s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/python3.10/site-packages'
[0.611s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/bin'
[0.611s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.ps1'
[0.612s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.dsv'
[0.612s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.sh'
[0.613s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.bash'
[0.613s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.zsh'
[0.613s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/colcon-core/packages/visualization_tools)
[0.613s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.613s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.614s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.614s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.618s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.618s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.618s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.625s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.626s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.ps1'
[0.626s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/saoxueche0808pm/saoxueche/install/_local_setup_util_ps1.py'
[0.627s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.ps1'
[0.628s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.sh'
[0.628s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/saoxueche0808pm/saoxueche/install/_local_setup_util_sh.py'
[0.629s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.sh'
[0.630s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.bash'
[0.630s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.bash'
[0.631s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.zsh'
[0.631s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.zsh'
