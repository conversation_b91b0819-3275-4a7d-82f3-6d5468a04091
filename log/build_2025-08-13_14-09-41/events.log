[0.000000] (-) TimerEvent: {}
[0.000679] (integrated_navigation) JobQueued: {'identifier': 'integrated_navigation', 'dependencies': OrderedDict()}
[0.000770] (loam_interface) JobQueued: {'identifier': 'loam_interface', 'dependencies': OrderedDict()}
[0.000801] (local_planner) JobQueued: {'identifier': 'local_planner', 'dependencies': OrderedDict()}
[0.000836] (nav2_smac_test) JobQueued: {'identifier': 'nav2_smac_test', 'dependencies': OrderedDict()}
[0.000908] (sensor_scan_generation) JobQueued: {'identifier': 'sensor_scan_generation', 'dependencies': OrderedDict()}
[0.000934] (terrain_analysis) JobQueued: {'identifier': 'terrain_analysis', 'dependencies': OrderedDict()}
[0.000959] (terrain_analysis_ext) JobQueued: {'identifier': 'terrain_analysis_ext', 'dependencies': OrderedDict()}
[0.001005] (visualization_tools) JobQueued: {'identifier': 'visualization_tools', 'dependencies': OrderedDict()}
[0.001060] (waypoint_example) JobQueued: {'identifier': 'waypoint_example', 'dependencies': OrderedDict()}
[0.001086] (waypoint_rviz_plugin) JobQueued: {'identifier': 'waypoint_rviz_plugin', 'dependencies': OrderedDict()}
[0.001117] (integrated_navigation) JobStarted: {'identifier': 'integrated_navigation'}
[0.005625] (loam_interface) JobStarted: {'identifier': 'loam_interface'}
[0.008702] (local_planner) JobStarted: {'identifier': 'local_planner'}
[0.011465] (nav2_smac_test) JobStarted: {'identifier': 'nav2_smac_test'}
[0.014617] (sensor_scan_generation) JobStarted: {'identifier': 'sensor_scan_generation'}
[0.017885] (terrain_analysis) JobStarted: {'identifier': 'terrain_analysis'}
[0.020950] (terrain_analysis_ext) JobStarted: {'identifier': 'terrain_analysis_ext'}
[0.024013] (visualization_tools) JobStarted: {'identifier': 'visualization_tools'}
[0.027121] (waypoint_example) JobStarted: {'identifier': 'waypoint_example'}
[0.030249] (waypoint_rviz_plugin) JobStarted: {'identifier': 'waypoint_rviz_plugin'}
[0.035650] (integrated_navigation) JobProgress: {'identifier': 'integrated_navigation', 'progress': 'cmake'}
[0.035947] (integrated_navigation) JobProgress: {'identifier': 'integrated_navigation', 'progress': 'build'}
[0.036434] (integrated_navigation) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.038176] (loam_interface) JobProgress: {'identifier': 'loam_interface', 'progress': 'cmake'}
[0.038803] (loam_interface) JobProgress: {'identifier': 'loam_interface', 'progress': 'build'}
[0.039119] (loam_interface) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.039983] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'cmake'}
[0.040478] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'build'}
[0.041111] (local_planner) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.041966] (nav2_smac_test) JobProgress: {'identifier': 'nav2_smac_test', 'progress': 'cmake'}
[0.042458] (nav2_smac_test) JobProgress: {'identifier': 'nav2_smac_test', 'progress': 'build'}
[0.042705] (nav2_smac_test) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.044082] (sensor_scan_generation) JobProgress: {'identifier': 'sensor_scan_generation', 'progress': 'cmake'}
[0.044872] (sensor_scan_generation) JobProgress: {'identifier': 'sensor_scan_generation', 'progress': 'build'}
[0.045513] (sensor_scan_generation) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.046889] (terrain_analysis) JobProgress: {'identifier': 'terrain_analysis', 'progress': 'cmake'}
[0.047179] (terrain_analysis) JobProgress: {'identifier': 'terrain_analysis', 'progress': 'build'}
[0.047431] (terrain_analysis) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.049468] (terrain_analysis_ext) JobProgress: {'identifier': 'terrain_analysis_ext', 'progress': 'cmake'}
[0.050015] (terrain_analysis_ext) JobProgress: {'identifier': 'terrain_analysis_ext', 'progress': 'build'}
[0.050644] (terrain_analysis_ext) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.052695] (visualization_tools) JobProgress: {'identifier': 'visualization_tools', 'progress': 'cmake'}
[0.053572] (visualization_tools) JobProgress: {'identifier': 'visualization_tools', 'progress': 'build'}
[0.054190] (visualization_tools) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.056964] (waypoint_example) JobProgress: {'identifier': 'waypoint_example', 'progress': 'cmake'}
[0.057367] (waypoint_example) JobProgress: {'identifier': 'waypoint_example', 'progress': 'build'}
[0.057946] (waypoint_example) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.065959] (waypoint_rviz_plugin) JobProgress: {'identifier': 'waypoint_rviz_plugin', 'progress': 'cmake'}
[0.066678] (waypoint_rviz_plugin) JobProgress: {'identifier': 'waypoint_rviz_plugin', 'progress': 'build'}
[0.067179] (waypoint_rviz_plugin) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.072336] (integrated_navigation) CommandEnded: {'returncode': 0}
[0.073249] (integrated_navigation) JobProgress: {'identifier': 'integrated_navigation', 'progress': 'install'}
[0.080791] (integrated_navigation) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.082522] (nav2_smac_test) CommandEnded: {'returncode': 0}
[0.083276] (nav2_smac_test) JobProgress: {'identifier': 'nav2_smac_test', 'progress': 'install'}
[0.083690] (nav2_smac_test) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.088792] (integrated_navigation) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.089060] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/launch\n'}
[0.089167] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/launch/no_smac_test.launch.py\n'}
[0.089238] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/launch/algorithm_only.launch.py\n'}
[0.089293] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/launch/integrated_navigation.launch.py\n'}
[0.089346] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/launch/test_smac_simple.launch.py\n'}
[0.089394] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/launch/system_real_robot_map.launch.py\n'}
[0.089441] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/config\n'}
[0.089488] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/config/integrated_navigation.rviz\n'}
[0.089533] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/config/integrated_nav_params.yaml\n'}
[0.089578] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/config/system_real_robot.rviz\n'}
[0.089625] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/maps\n'}
[0.089677] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/maps/test_map.pgm\n'}
[0.089737] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/maps/test_map.yaml\n'}
[0.089792] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/ament_index/resource_index/package_run_dependencies/integrated_navigation\n'}
[0.089843] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/ament_index/resource_index/parent_prefix_path/integrated_navigation\n'}
[0.089938] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/environment/ament_prefix_path.sh\n'}
[0.090050] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/environment/ament_prefix_path.dsv\n'}
[0.090113] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/environment/path.sh\n'}
[0.090163] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/environment/path.dsv\n'}
[0.090251] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/local_setup.bash\n'}
[0.090359] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/local_setup.sh\n'}
[0.090413] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/local_setup.zsh\n'}
[0.090464] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/local_setup.dsv\n'}
[0.090513] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.dsv\n'}
[0.090604] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/ament_index/resource_index/packages/integrated_navigation\n'}
[0.090661] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/cmake/integrated_navigationConfig.cmake\n'}
[0.090711] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/cmake/integrated_navigationConfig-version.cmake\n'}
[0.090764] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.xml\n'}
[0.090812] (terrain_analysis) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target terrainAnalysis\x1b[0m\n'}
[0.091022] (nav2_smac_test) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.091610] (local_planner) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target localPlanner\x1b[0m\n'}
[0.091968] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//launch\n'}
[0.092227] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//launch/simulation_launch.py\n'}
[0.092295] (sensor_scan_generation) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target sensorScanGeneration\x1b[0m\n'}
[0.092382] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//launch/smac_planner_test.launch.py\n'}
[0.092418] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//launch/simulation_launch_fixed.py\n'}
[0.092448] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//launch/simple_planner_test.launch.py\n'}
[0.092480] (integrated_navigation) CommandEnded: {'returncode': 0}
[0.094775] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//config\n'}
[0.095089] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//config/nav2_params.yaml\n'}
[0.095320] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//maps\n'}
[0.095527] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//maps/test_map.pgm\n'}
[0.097353] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//maps/test_map.yaml\n'}
[0.098425] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//worlds\n'}
[0.098572] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//worlds/test_world.world\n'}
[0.098657] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//urdf\n'}
[0.098737] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//urdf/robot.urdf.xacro\n'}
[0.098875] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/ament_index/resource_index/package_run_dependencies/nav2_smac_test\n'}
[0.099007] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/ament_index/resource_index/parent_prefix_path/nav2_smac_test\n'}
[0.099100] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/environment/ament_prefix_path.sh\n'}
[0.099185] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/environment/ament_prefix_path.dsv\n'}
[0.099311] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/environment/path.sh\n'}
[0.099395] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/environment/path.dsv\n'}
[0.099473] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/local_setup.bash\n'}
[0.099550] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/local_setup.sh\n'}
[0.099628] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/local_setup.zsh\n'}
[0.099702] (-) TimerEvent: {}
[0.099909] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/local_setup.dsv\n'}
[0.100024] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.dsv\n'}
[0.100115] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/ament_index/resource_index/packages/nav2_smac_test\n'}
[0.100174] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/cmake/nav2_smac_testConfig.cmake\n'}
[0.100241] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/cmake/nav2_smac_testConfig-version.cmake\n'}
[0.100279] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.xml\n'}
[0.100313] (loam_interface) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target loamInterface\x1b[0m\n'}
[0.100608] (terrain_analysis_ext) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target terrainAnalysisExt\x1b[0m\n'}
[0.100678] (visualization_tools) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target visualizationTools\x1b[0m\n'}
[0.100823] (waypoint_example) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target waypointExample\x1b[0m\n'}
[0.105770] (local_planner) StdoutLine: {'line': b'[ 50%] Built target pathFollower\n'}
[0.111527] (terrain_analysis) StdoutLine: {'line': b'[100%] Built target terrainAnalysis\n'}
[0.111814] (waypoint_rviz_plugin) StdoutLine: {'line': b'[ 16%] \x1b[34m\x1b[1mAutomatic MOC for target waypoint_rviz_plugin\x1b[0m\n'}
[0.111976] (local_planner) StdoutLine: {'line': b'[100%] Built target localPlanner\n'}
[0.115552] (terrain_analysis_ext) StdoutLine: {'line': b'[100%] Built target terrainAnalysisExt\n'}
[0.116085] (visualization_tools) StdoutLine: {'line': b'[  0%] Built target ament_cmake_python_copy_visualization_tools\n'}
[0.116179] (sensor_scan_generation) StdoutLine: {'line': b'[100%] Built target sensorScanGeneration\n'}
[0.117735] (integrated_navigation) JobEnded: {'identifier': 'integrated_navigation', 'rc': 0}
[0.119100] (nav2_smac_test) CommandEnded: {'returncode': 0}
[0.119580] (loam_interface) StdoutLine: {'line': b'[100%] Built target loamInterface\n'}
[0.120848] (visualization_tools) StdoutLine: {'line': b'[100%] Built target visualizationTools\n'}
[0.124262] (waypoint_rviz_plugin) StdoutLine: {'line': b'[ 16%] Built target waypoint_rviz_plugin_autogen\n'}
[0.125396] (waypoint_example) StdoutLine: {'line': b'[100%] Built target waypointExample\n'}
[0.127748] (nav2_smac_test) JobEnded: {'identifier': 'nav2_smac_test', 'rc': 0}
[0.129224] (terrain_analysis) CommandEnded: {'returncode': 0}
[0.129867] (terrain_analysis) JobProgress: {'identifier': 'terrain_analysis', 'progress': 'install'}
[0.130580] (terrain_analysis) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.131636] (local_planner) CommandEnded: {'returncode': 0}
[0.132090] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'install'}
[0.132157] (waypoint_rviz_plugin) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target waypoint_rviz_plugin\x1b[0m\n'}
[0.132756] (local_planner) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.133807] (terrain_analysis_ext) CommandEnded: {'returncode': 0}
[0.134353] (terrain_analysis_ext) JobProgress: {'identifier': 'terrain_analysis_ext', 'progress': 'install'}
[0.134866] (terrain_analysis_ext) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.135788] (sensor_scan_generation) CommandEnded: {'returncode': 0}
[0.136269] (sensor_scan_generation) JobProgress: {'identifier': 'sensor_scan_generation', 'progress': 'install'}
[0.136855] (sensor_scan_generation) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.137823] (loam_interface) CommandEnded: {'returncode': 0}
[0.138533] (terrain_analysis) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.139915] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib/terrain_analysis/terrainAnalysis\n'}
[0.140449] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/launch\n'}
[0.141119] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/launch/terrain_analysis.launch\n'}
[0.141538] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/ament_index/resource_index/package_run_dependencies/terrain_analysis\n'}
[0.141615] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/ament_index/resource_index/parent_prefix_path/terrain_analysis\n'}
[0.141676] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/environment/ament_prefix_path.sh\n'}
[0.141733] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/environment/ament_prefix_path.dsv\n'}
[0.141790] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/environment/path.sh\n'}
[0.141843] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/environment/path.dsv\n'}
[0.141898] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/local_setup.bash\n'}
[0.141953] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/local_setup.sh\n'}
[0.142008] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/local_setup.zsh\n'}
[0.142062] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/local_setup.dsv\n'}
[0.142114] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.dsv\n'}
[0.142165] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/ament_index/resource_index/packages/terrain_analysis\n'}
[0.143221] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/cmake/terrain_analysisConfig.cmake\n'}
[0.144058] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/cmake/terrain_analysisConfig-version.cmake\n'}
[0.144120] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.xml\n'}
[0.144159] (local_planner) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.144199] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/localPlanner\n'}
[0.144248] (loam_interface) JobProgress: {'identifier': 'loam_interface', 'progress': 'install'}
[0.144262] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/pathFollower\n'}
[0.144296] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch\n'}
[0.144328] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch/local_planner.launch.py\n'}
[0.144359] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch/local_planner.launch\n'}
[0.144390] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths\n'}
[0.144420] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/startPaths.ply\n'}
[0.144449] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/correspondences.txt\n'}
[0.144478] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/pathList.ply\n'}
[0.144508] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/paths.ply\n'}
[0.144537] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/path_generator.m\n'}
[0.144567] (loam_interface) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.144796] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/config\n'}
[0.144837] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/package_run_dependencies/local_planner\n'}
[0.144871] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/parent_prefix_path/local_planner\n'}
[0.144902] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/ament_prefix_path.sh\n'}
[0.144933] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/ament_prefix_path.dsv\n'}
[0.144962] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/path.sh\n'}
[0.145081] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/path.dsv\n'}
[0.145125] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.bash\n'}
[0.145160] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.sh\n'}
[0.145194] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.zsh\n'}
[0.145237] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.dsv\n'}
[0.145271] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv\n'}
[0.145332] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/packages/local_planner\n'}
[0.145365] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/cmake/local_plannerConfig.cmake\n'}
[0.145396] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/cmake/local_plannerConfig-version.cmake\n'}
[0.145449] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.xml\n'}
[0.145482] (terrain_analysis_ext) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.145520] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib/terrain_analysis_ext/terrainAnalysisExt\n'}
[0.145552] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/launch\n'}
[0.145584] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/launch/terrain_analysis_ext.launch\n'}
[0.145614] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/ament_index/resource_index/package_run_dependencies/terrain_analysis_ext\n'}
[0.145644] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/ament_index/resource_index/parent_prefix_path/terrain_analysis_ext\n'}
[0.145675] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/ament_prefix_path.sh\n'}
[0.145707] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/ament_prefix_path.dsv\n'}
[0.145737] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/path.sh\n'}
[0.145766] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/path.dsv\n'}
[0.145795] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.bash\n'}
[0.145825] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.sh\n'}
[0.145854] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.zsh\n'}
[0.145883] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.dsv\n'}
[0.145912] (terrain_analysis_ext) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.dsv\n'}
[0.145963] (waypoint_example) CommandEnded: {'returncode': 0}
[0.146181] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/ament_index/resource_index/packages/terrain_analysis_ext\n'}
[0.146232] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/cmake/terrain_analysis_extConfig.cmake\n'}
[0.146283] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/cmake/terrain_analysis_extConfig-version.cmake\n'}
[0.146341] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.xml\n'}
[0.146389] (sensor_scan_generation) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.146428] (waypoint_example) JobProgress: {'identifier': 'waypoint_example', 'progress': 'install'}
[0.146440] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib/sensor_scan_generation/sensorScanGeneration\n'}
[0.146474] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/launch\n'}
[0.146520] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/launch/sensor_scan_generation.launch\n'}
[0.146566] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/ament_index/resource_index/package_run_dependencies/sensor_scan_generation\n'}
[0.146596] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/ament_index/resource_index/parent_prefix_path/sensor_scan_generation\n'}
[0.146640] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/environment/ament_prefix_path.sh\n'}
[0.146670] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/environment/ament_prefix_path.dsv\n'}
[0.146699] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/environment/path.sh\n'}
[0.146726] (waypoint_example) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.146945] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/environment/path.dsv\n'}
[0.146985] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.bash\n'}
[0.147018] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.sh\n'}
[0.147050] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.zsh\n'}
[0.147081] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.dsv\n'}
[0.147111] (sensor_scan_generation) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv\n'}
[0.147140] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/ament_index/resource_index/packages/sensor_scan_generation\n'}
[0.147171] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/cmake/sensor_scan_generationConfig.cmake\n'}
[0.147200] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/cmake/sensor_scan_generationConfig-version.cmake\n'}
[0.147243] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.xml\n'}
[0.147300] (terrain_analysis) CommandEnded: {'returncode': 0}
[0.147488] (loam_interface) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.147535] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib/loam_interface/loamInterface\n'}
[0.147578] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/launch\n'}
[0.147609] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/launch/loam_interface.launch\n'}
[0.147685] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/ament_index/resource_index/package_run_dependencies/loam_interface\n'}
[0.147716] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/ament_index/resource_index/parent_prefix_path/loam_interface\n'}
[0.147746] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/environment/ament_prefix_path.sh\n'}
[0.147775] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/environment/ament_prefix_path.dsv\n'}
[0.147803] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/environment/path.sh\n'}
[0.147832] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/environment/path.dsv\n'}
[0.147860] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/local_setup.bash\n'}
[0.147889] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/local_setup.sh\n'}
[0.147916] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/local_setup.zsh\n'}
[0.147945] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/local_setup.dsv\n'}
[0.147974] (loam_interface) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.dsv\n'}
[0.148002] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/ament_index/resource_index/packages/loam_interface\n'}
[0.148032] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/cmake/loam_interfaceConfig.cmake\n'}
[0.148061] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/cmake/loam_interfaceConfig-version.cmake\n'}
[0.148090] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.xml\n'}
[0.148119] (waypoint_example) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.148153] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib/waypoint_example/waypointExample\n'}
[0.148183] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/launch\n'}
[0.148220] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/launch/waypoint_example_garage.launch\n'}
[0.148349] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/data\n'}
[0.148386] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/data/waypoints_garage.ply\n'}
[0.148417] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/data/boundary_garage.ply\n'}
[0.148449] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/ament_index/resource_index/package_run_dependencies/waypoint_example\n'}
[0.148479] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/ament_index/resource_index/parent_prefix_path/waypoint_example\n'}
[0.148545] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/environment/ament_prefix_path.sh\n'}
[0.148575] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/environment/ament_prefix_path.dsv\n'}
[0.148604] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/environment/path.sh\n'}
[0.148633] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/environment/path.dsv\n'}
[0.148662] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/local_setup.bash\n'}
[0.148691] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/local_setup.sh\n'}
[0.148719] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/local_setup.zsh\n'}
[0.148747] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/local_setup.dsv\n'}
[0.148776] (waypoint_example) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.dsv\n'}
[0.148804] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/ament_index/resource_index/packages/waypoint_example\n'}
[0.148832] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/cmake/waypoint_exampleConfig.cmake\n'}
[0.148861] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/cmake/waypoint_exampleConfig-version.cmake\n'}
[0.148889] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.xml\n'}
[0.149834] (waypoint_rviz_plugin) StdoutLine: {'line': b'[100%] Built target waypoint_rviz_plugin\n'}
[0.156233] (terrain_analysis) JobEnded: {'identifier': 'terrain_analysis', 'rc': 0}
[0.156495] (local_planner) CommandEnded: {'returncode': 0}
[0.164317] (local_planner) JobEnded: {'identifier': 'local_planner', 'rc': 0}
[0.165742] (sensor_scan_generation) CommandEnded: {'returncode': 0}
[0.172047] (sensor_scan_generation) JobEnded: {'identifier': 'sensor_scan_generation', 'rc': 0}
[0.172694] (terrain_analysis_ext) CommandEnded: {'returncode': 0}
[0.179269] (terrain_analysis_ext) JobEnded: {'identifier': 'terrain_analysis_ext', 'rc': 0}
[0.180233] (loam_interface) CommandEnded: {'returncode': 0}
[0.186312] (loam_interface) JobEnded: {'identifier': 'loam_interface', 'rc': 0}
[0.187696] (waypoint_rviz_plugin) CommandEnded: {'returncode': 0}
[0.187961] (waypoint_rviz_plugin) JobProgress: {'identifier': 'waypoint_rviz_plugin', 'progress': 'install'}
[0.187981] (waypoint_rviz_plugin) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.188218] (waypoint_example) CommandEnded: {'returncode': 0}
[0.194429] (waypoint_example) JobEnded: {'identifier': 'waypoint_example', 'rc': 0}
[0.195521] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.195621] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/plugin_description.xml\n'}
[0.195686] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib/waypoint_rviz_plugin/libwaypoint_rviz_plugin.so\n'}
[0.195772] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/ament_index/resource_index/package_run_dependencies/waypoint_rviz_plugin\n'}
[0.195827] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/ament_index/resource_index/parent_prefix_path/waypoint_rviz_plugin\n'}
[0.195894] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/ament_prefix_path.sh\n'}
[0.195930] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/ament_prefix_path.dsv\n'}
[0.195988] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/path.sh\n'}
[0.196021] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/path.dsv\n'}
[0.196052] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.bash\n'}
[0.196113] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.sh\n'}
[0.196147] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.zsh\n'}
[0.196180] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.dsv\n'}
[0.196213] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.dsv\n'}
[0.196273] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/ament_index/resource_index/packages/waypoint_rviz_plugin\n'}
[0.196307] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/ament_index/resource_index/rviz_common__pluginlib__plugin/waypoint_rviz_plugin\n'}
[0.196339] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.196396] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/cmake/waypoint_rviz_pluginConfig.cmake\n'}
[0.196427] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/cmake/waypoint_rviz_pluginConfig-version.cmake\n'}
[0.196456] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.xml\n'}
[0.197384] (waypoint_rviz_plugin) CommandEnded: {'returncode': 0}
[0.199932] (-) TimerEvent: {}
[0.204245] (waypoint_rviz_plugin) JobEnded: {'identifier': 'waypoint_rviz_plugin', 'rc': 0}
[0.273723] (visualization_tools) StdoutLine: {'line': b'running egg_info\n'}
[0.274129] (visualization_tools) StdoutLine: {'line': b'writing visualization_tools.egg-info/PKG-INFO\n'}
[0.274237] (visualization_tools) StdoutLine: {'line': b'writing dependency_links to visualization_tools.egg-info/dependency_links.txt\n'}
[0.274341] (visualization_tools) StdoutLine: {'line': b'writing top-level names to visualization_tools.egg-info/top_level.txt\n'}
[0.275943] (visualization_tools) StdoutLine: {'line': b"reading manifest file 'visualization_tools.egg-info/SOURCES.txt'\n"}
[0.276213] (visualization_tools) StdoutLine: {'line': b"writing manifest file 'visualization_tools.egg-info/SOURCES.txt'\n"}
[0.296154] (visualization_tools) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_visualization_tools_egg\n'}
[0.300011] (-) TimerEvent: {}
[0.304973] (visualization_tools) CommandEnded: {'returncode': 0}
[0.305570] (visualization_tools) JobProgress: {'identifier': 'visualization_tools', 'progress': 'install'}
[0.305957] (visualization_tools) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/saoxueche/install/velodyne_gazebo_plugins/lib:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install:/home/<USER>/saoxueche/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', '/home/<USER>/saoxueche/install/velodyne_description/share/'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:86e93a02-2426-4e3f-90d9-48368ab3f2a6'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/home/<USER>/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example:/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test:/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner:/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface:/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation:/home/<USER>/saoxueche/install/waypoint_rviz_plugin:/home/<USER>/saoxueche/install/waypoint_example:/home/<USER>/saoxueche/install/visualization_tools:/home/<USER>/saoxueche/install/velodyne_simulator:/home/<USER>/saoxueche/install/velodyne_gazebo_plugins:/home/<USER>/saoxueche/install/velodyne_description:/home/<USER>/saoxueche/install/vehicle_simulator:/home/<USER>/saoxueche/install/terrain_analysis_ext:/home/<USER>/saoxueche/install/terrain_analysis:/home/<USER>/saoxueche/install/sensor_scan_generation:/home/<USER>/saoxueche/install/nav2_smac:/home/<USER>/saoxueche/install/local_planner:/home/<USER>/saoxueche/install/loam_interface:/home/<USER>/saoxueche/install/integrated_navigation:/opt/ros/humble')]), 'shell': False}
[0.311148] (visualization_tools) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.311284] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/visualization_tools/visualizationTools\n'}
[0.311331] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/launch\n'}
[0.311397] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/launch/visualization_tools.launch\n'}
[0.311427] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/pythonpath.sh\n'}
[0.311485] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/pythonpath.dsv\n'}
[0.311514] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info\n'}
[0.311543] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/dependency_links.txt\n'}
[0.311609] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/SOURCES.txt\n'}
[0.311680] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/top_level.txt\n'}
[0.311712] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/PKG-INFO\n'}
[0.311741] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools\n'}
[0.311769] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools/__init__.py\n'}
[0.333395] (visualization_tools) StdoutLine: {'line': b"Listing '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools'...\n"}
[0.336264] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/visualization_tools/realTimePlot.py\n'}
[0.336503] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/ament_index/resource_index/package_run_dependencies/visualization_tools\n'}
[0.336616] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/ament_index/resource_index/parent_prefix_path/visualization_tools\n'}
[0.336709] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/ament_prefix_path.sh\n'}
[0.336798] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/ament_prefix_path.dsv\n'}
[0.336914] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/path.sh\n'}
[0.336999] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/path.dsv\n'}
[0.337081] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/local_setup.bash\n'}
[0.337161] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/local_setup.sh\n'}
[0.337247] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/local_setup.zsh\n'}
[0.337333] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/local_setup.dsv\n'}
[0.337425] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.dsv\n'}
[0.337506] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/ament_index/resource_index/packages/visualization_tools\n'}
[0.337599] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/cmake/visualization_toolsConfig.cmake\n'}
[0.337678] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/cmake/visualization_toolsConfig-version.cmake\n'}
[0.337758] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.xml\n'}
[0.338700] (visualization_tools) CommandEnded: {'returncode': 0}
[0.347151] (visualization_tools) JobEnded: {'identifier': 'visualization_tools', 'rc': 0}
[0.347821] (-) EventReactorShutdown: {}
