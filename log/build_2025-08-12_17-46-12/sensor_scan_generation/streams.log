[0.026s] Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation -- -j16 -l16
[0.065s] [35m[1mConsolidate compiler generated dependencies of target sensorScanGeneration[0m
[0.099s] [100%] Built target sensorScanGeneration
[0.111s] Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation -- -j16 -l16
[0.112s] Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation
[0.122s] -- Install configuration: ""
[0.122s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib/sensor_scan_generation/sensorScanGeneration
[0.122s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/launch
[0.122s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/launch/sensor_scan_generation.launch
[0.122s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/ament_index/resource_index/package_run_dependencies/sensor_scan_generation
[0.122s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/ament_index/resource_index/parent_prefix_path/sensor_scan_generation
[0.122s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/environment/ament_prefix_path.sh
[0.122s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/environment/ament_prefix_path.dsv
[0.122s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/environment/path.sh
[0.122s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/environment/path.dsv
[0.122s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.bash
[0.122s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.sh
[0.122s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.zsh
[0.122s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.dsv
[0.123s] -- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv
[0.124s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/ament_index/resource_index/packages/sensor_scan_generation
[0.124s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/cmake/sensor_scan_generationConfig.cmake
[0.125s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/cmake/sensor_scan_generationConfig-version.cmake
[0.126s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.xml
[0.134s] Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation
