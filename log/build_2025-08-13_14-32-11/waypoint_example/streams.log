[0.044s] Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example -- -j16 -l16
[0.108s] [100%] Built target waypointExample
[0.124s] Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example -- -j16 -l16
[0.125s] Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example
[0.134s] -- Install configuration: ""
[0.134s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib/waypoint_example/waypointExample
[0.134s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/launch
[0.134s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/launch/waypoint_example_garage.launch
[0.134s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/data
[0.135s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/data/waypoints_garage.ply
[0.135s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/data/boundary_garage.ply
[0.135s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/ament_index/resource_index/package_run_dependencies/waypoint_example
[0.135s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/ament_index/resource_index/parent_prefix_path/waypoint_example
[0.135s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/environment/ament_prefix_path.sh
[0.135s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/environment/ament_prefix_path.dsv
[0.136s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/environment/path.sh
[0.136s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/environment/path.dsv
[0.136s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/local_setup.bash
[0.136s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/local_setup.sh
[0.136s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/local_setup.zsh
[0.136s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/local_setup.dsv
[0.137s] -- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.dsv
[0.137s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/ament_index/resource_index/packages/waypoint_example
[0.137s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/cmake/waypoint_exampleConfig.cmake
[0.137s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/cmake/waypoint_exampleConfig-version.cmake
[0.137s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.xml
[0.169s] Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example
