[0.000000] (-) TimerEvent: {}
[0.000448] (integrated_navigation) JobQueued: {'identifier': 'integrated_navigation', 'dependencies': OrderedDict()}
[0.000583] (loam_interface) JobQueued: {'identifier': 'loam_interface', 'dependencies': OrderedDict()}
[0.000596] (local_planner) JobQueued: {'identifier': 'local_planner', 'dependencies': OrderedDict()}
[0.000605] (nav2_smac_test) JobQueued: {'identifier': 'nav2_smac_test', 'dependencies': OrderedDict()}
[0.000614] (sensor_scan_generation) JobQueued: {'identifier': 'sensor_scan_generation', 'dependencies': OrderedDict()}
[0.000623] (terrain_analysis) JobQueued: {'identifier': 'terrain_analysis', 'dependencies': OrderedDict()}
[0.000631] (terrain_analysis_ext) JobQueued: {'identifier': 'terrain_analysis_ext', 'dependencies': OrderedDict()}
[0.000640] (visualization_tools) JobQueued: {'identifier': 'visualization_tools', 'dependencies': OrderedDict()}
[0.000648] (waypoint_example) JobQueued: {'identifier': 'waypoint_example', 'dependencies': OrderedDict()}
[0.000657] (waypoint_rviz_plugin) JobQueued: {'identifier': 'waypoint_rviz_plugin', 'dependencies': OrderedDict()}
[0.000665] (integrated_navigation) JobStarted: {'identifier': 'integrated_navigation'}
[0.005032] (loam_interface) JobStarted: {'identifier': 'loam_interface'}
[0.007769] (local_planner) JobStarted: {'identifier': 'local_planner'}
[0.009678] (nav2_smac_test) JobStarted: {'identifier': 'nav2_smac_test'}
[0.011789] (sensor_scan_generation) JobStarted: {'identifier': 'sensor_scan_generation'}
[0.014051] (terrain_analysis) JobStarted: {'identifier': 'terrain_analysis'}
[0.016199] (terrain_analysis_ext) JobStarted: {'identifier': 'terrain_analysis_ext'}
[0.018320] (visualization_tools) JobStarted: {'identifier': 'visualization_tools'}
[0.020559] (waypoint_example) JobStarted: {'identifier': 'waypoint_example'}
[0.022667] (waypoint_rviz_plugin) JobStarted: {'identifier': 'waypoint_rviz_plugin'}
[0.026431] (integrated_navigation) JobProgress: {'identifier': 'integrated_navigation', 'progress': 'cmake'}
[0.027267] (integrated_navigation) JobProgress: {'identifier': 'integrated_navigation', 'progress': 'build'}
[0.027397] (integrated_navigation) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.029436] (loam_interface) JobProgress: {'identifier': 'loam_interface', 'progress': 'cmake'}
[0.029722] (loam_interface) JobProgress: {'identifier': 'loam_interface', 'progress': 'build'}
[0.030434] (loam_interface) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.031749] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'cmake'}
[0.031948] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'build'}
[0.032604] (local_planner) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.033638] (nav2_smac_test) JobProgress: {'identifier': 'nav2_smac_test', 'progress': 'cmake'}
[0.033746] (nav2_smac_test) JobProgress: {'identifier': 'nav2_smac_test', 'progress': 'build'}
[0.033961] (nav2_smac_test) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.035829] (sensor_scan_generation) JobProgress: {'identifier': 'sensor_scan_generation', 'progress': 'cmake'}
[0.036168] (sensor_scan_generation) JobProgress: {'identifier': 'sensor_scan_generation', 'progress': 'build'}
[0.037653] (sensor_scan_generation) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.041805] (terrain_analysis) JobProgress: {'identifier': 'terrain_analysis', 'progress': 'cmake'}
[0.042436] (terrain_analysis) JobProgress: {'identifier': 'terrain_analysis', 'progress': 'build'}
[0.043936] (terrain_analysis) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.047282] (terrain_analysis_ext) JobProgress: {'identifier': 'terrain_analysis_ext', 'progress': 'cmake'}
[0.047540] (terrain_analysis_ext) JobProgress: {'identifier': 'terrain_analysis_ext', 'progress': 'build'}
[0.048844] (terrain_analysis_ext) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.052295] (visualization_tools) JobProgress: {'identifier': 'visualization_tools', 'progress': 'cmake'}
[0.053269] (visualization_tools) JobProgress: {'identifier': 'visualization_tools', 'progress': 'build'}
[0.054446] (visualization_tools) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.058066] (waypoint_example) JobProgress: {'identifier': 'waypoint_example', 'progress': 'cmake'}
[0.058772] (waypoint_example) JobProgress: {'identifier': 'waypoint_example', 'progress': 'build'}
[0.059958] (waypoint_example) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.070248] (waypoint_rviz_plugin) JobProgress: {'identifier': 'waypoint_rviz_plugin', 'progress': 'cmake'}
[0.071353] (waypoint_rviz_plugin) JobProgress: {'identifier': 'waypoint_rviz_plugin', 'progress': 'build'}
[0.073100] (waypoint_rviz_plugin) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.075949] (integrated_navigation) CommandEnded: {'returncode': 0}
[0.077438] (integrated_navigation) JobProgress: {'identifier': 'integrated_navigation', 'progress': 'install'}
[0.086657] (integrated_navigation) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.088100] (nav2_smac_test) CommandEnded: {'returncode': 0}
[0.088937] (nav2_smac_test) JobProgress: {'identifier': 'nav2_smac_test', 'progress': 'install'}
[0.089485] (nav2_smac_test) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.093462] (integrated_navigation) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.093782] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/launch\n'}
[0.093871] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/launch/no_smac_test.launch.py\n'}
[0.093945] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/launch/algorithm_only.launch.py\n'}
[0.094005] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/launch/integrated_navigation.launch.py\n'}
[0.094065] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/launch/test_smac_simple.launch.py\n'}
[0.094123] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/launch/system_real_robot_map.launch.py\n'}
[0.094180] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/config\n'}
[0.094231] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/config/integrated_navigation.rviz\n'}
[0.094318] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/config/integrated_nav_params.yaml\n'}
[0.094373] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/config/system_real_robot.rviz\n'}
[0.094427] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/maps\n'}
[0.094484] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/maps/test_map.pgm\n'}
[0.094541] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/maps/test_map.yaml\n'}
[0.094672] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/ament_index/resource_index/package_run_dependencies/integrated_navigation\n'}
[0.094840] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/ament_index/resource_index/parent_prefix_path/integrated_navigation\n'}
[0.094914] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/environment/ament_prefix_path.sh\n'}
[0.094974] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/environment/ament_prefix_path.dsv\n'}
[0.095029] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/environment/path.sh\n'}
[0.095083] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/environment/path.dsv\n'}
[0.095135] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/local_setup.bash\n'}
[0.095315] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/local_setup.sh\n'}
[0.095455] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/local_setup.zsh\n'}
[0.095563] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/local_setup.dsv\n'}
[0.095674] (integrated_navigation) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.dsv\n'}
[0.095781] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/ament_index/resource_index/packages/integrated_navigation\n'}
[0.095874] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/cmake/integrated_navigationConfig.cmake\n'}
[0.095963] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/cmake/integrated_navigationConfig-version.cmake\n'}
[0.096052] (integrated_navigation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.xml\n'}
[0.097171] (nav2_smac_test) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.097674] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//launch\n'}
[0.098396] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//launch/simulation_launch.py\n'}
[0.098486] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//launch/smac_planner_test.launch.py\n'}
[0.098550] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//launch/simulation_launch_fixed.py\n'}
[0.098609] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//launch/simple_planner_test.launch.py\n'}
[0.098662] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//config\n'}
[0.098717] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//config/nav2_params.yaml\n'}
[0.098767] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//maps\n'}
[0.098818] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//maps/test_map.pgm\n'}
[0.098872] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//maps/test_map.yaml\n'}
[0.098926] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//worlds\n'}
[0.098980] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//worlds/test_world.world\n'}
[0.099033] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//urdf\n'}
[0.099087] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test//urdf/robot.urdf.xacro\n'}
[0.099891] (integrated_navigation) CommandEnded: {'returncode': 0}
[0.100520] (-) TimerEvent: {}
[0.104407] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/ament_index/resource_index/package_run_dependencies/nav2_smac_test\n'}
[0.104988] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/ament_index/resource_index/parent_prefix_path/nav2_smac_test\n'}
[0.105075] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/environment/ament_prefix_path.sh\n'}
[0.105135] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/environment/ament_prefix_path.dsv\n'}
[0.105185] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/environment/path.sh\n'}
[0.105231] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/environment/path.dsv\n'}
[0.105451] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/local_setup.bash\n'}
[0.105600] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/local_setup.sh\n'}
[0.105718] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/local_setup.zsh\n'}
[0.105763] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/local_setup.dsv\n'}
[0.105805] (nav2_smac_test) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.dsv\n'}
[0.105845] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/ament_index/resource_index/packages/nav2_smac_test\n'}
[0.105884] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/cmake/nav2_smac_testConfig.cmake\n'}
[0.105948] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/cmake/nav2_smac_testConfig-version.cmake\n'}
[0.105994] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.xml\n'}
[0.111758] (local_planner) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/localPlanner.dir/src/localPlanner.cpp.o\x1b[0m\n'}
[0.112165] (loam_interface) StdoutLine: {'line': b'[100%] Built target loamInterface\n'}
[0.112938] (local_planner) StdoutLine: {'line': b'[ 75%] Built target pathFollower\n'}
[0.116359] (integrated_navigation) JobEnded: {'identifier': 'integrated_navigation', 'rc': 0}
[0.118161] (visualization_tools) StdoutLine: {'line': b'[  0%] Built target ament_cmake_python_copy_visualization_tools\n'}
[0.118428] (waypoint_rviz_plugin) StdoutLine: {'line': b'[ 16%] \x1b[34m\x1b[1mAutomatic MOC for target waypoint_rviz_plugin\x1b[0m\n'}
[0.118525] (nav2_smac_test) CommandEnded: {'returncode': 0}
[0.119000] (terrain_analysis) StdoutLine: {'line': b'[100%] Built target terrainAnalysis\n'}
[0.120908] (sensor_scan_generation) StdoutLine: {'line': b'[100%] Built target sensorScanGeneration\n'}
[0.127910] (visualization_tools) StdoutLine: {'line': b'[100%] Built target visualizationTools\n'}
[0.128389] (waypoint_example) StdoutLine: {'line': b'[100%] Built target waypointExample\n'}
[0.132195] (terrain_analysis_ext) StdoutLine: {'line': b'[100%] Built target terrainAnalysisExt\n'}
[0.134687] (waypoint_rviz_plugin) StdoutLine: {'line': b'[ 16%] Built target waypoint_rviz_plugin_autogen\n'}
[0.136312] (nav2_smac_test) JobEnded: {'identifier': 'nav2_smac_test', 'rc': 0}
[0.137940] (loam_interface) CommandEnded: {'returncode': 0}
[0.138525] (loam_interface) JobProgress: {'identifier': 'loam_interface', 'progress': 'install'}
[0.139053] (loam_interface) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.140519] (terrain_analysis) CommandEnded: {'returncode': 0}
[0.140975] (terrain_analysis) JobProgress: {'identifier': 'terrain_analysis', 'progress': 'install'}
[0.141262] (terrain_analysis) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.142456] (sensor_scan_generation) CommandEnded: {'returncode': 0}
[0.143686] (sensor_scan_generation) JobProgress: {'identifier': 'sensor_scan_generation', 'progress': 'install'}
[0.143841] (sensor_scan_generation) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.144231] (waypoint_example) CommandEnded: {'returncode': 0}
[0.144808] (waypoint_example) JobProgress: {'identifier': 'waypoint_example', 'progress': 'install'}
[0.145166] (waypoint_example) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.147379] (loam_interface) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.147560] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib/loam_interface/loamInterface\n'}
[0.147621] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/launch\n'}
[0.147674] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/launch/loam_interface.launch\n'}
[0.147724] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/ament_index/resource_index/package_run_dependencies/loam_interface\n'}
[0.147773] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/ament_index/resource_index/parent_prefix_path/loam_interface\n'}
[0.147823] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/environment/ament_prefix_path.sh\n'}
[0.147871] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/environment/ament_prefix_path.dsv\n'}
[0.147918] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/environment/path.sh\n'}
[0.147966] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/environment/path.dsv\n'}
[0.148079] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/local_setup.bash\n'}
[0.148130] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/local_setup.sh\n'}
[0.148439] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/local_setup.zsh\n'}
[0.148596] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/local_setup.dsv\n'}
[0.148648] (loam_interface) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.dsv\n'}
[0.148697] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/ament_index/resource_index/packages/loam_interface\n'}
[0.148746] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/cmake/loam_interfaceConfig.cmake\n'}
[0.148795] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/cmake/loam_interfaceConfig-version.cmake\n'}
[0.148846] (loam_interface) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.xml\n'}
[0.148893] (sensor_scan_generation) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.149158] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib/sensor_scan_generation/sensorScanGeneration\n'}
[0.149215] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/launch\n'}
[0.149269] (terrain_analysis_ext) CommandEnded: {'returncode': 0}
[0.149790] (terrain_analysis) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.149990] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/launch/sensor_scan_generation.launch\n'}
[0.150438] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib/terrain_analysis/terrainAnalysis\n'}
[0.150960] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/ament_index/resource_index/package_run_dependencies/sensor_scan_generation\n'}
[0.151587] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/ament_index/resource_index/parent_prefix_path/sensor_scan_generation\n'}
[0.151787] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/environment/ament_prefix_path.sh\n'}
[0.151844] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/environment/ament_prefix_path.dsv\n'}
[0.151895] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/environment/path.sh\n'}
[0.151952] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/environment/path.dsv\n'}
[0.152004] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.bash\n'}
[0.152439] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.sh\n'}
[0.152739] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.zsh\n'}
[0.152850] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/local_setup.dsv\n'}
[0.153041] (sensor_scan_generation) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv\n'}
[0.153201] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/ament_index/resource_index/packages/sensor_scan_generation\n'}
[0.153319] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/cmake/sensor_scan_generationConfig.cmake\n'}
[0.153450] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/cmake/sensor_scan_generationConfig-version.cmake\n'}
[0.153494] (sensor_scan_generation) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.xml\n'}
[0.153529] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/launch\n'}
[0.153579] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/launch/terrain_analysis.launch\n'}
[0.153613] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/ament_index/resource_index/package_run_dependencies/terrain_analysis\n'}
[0.153646] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/ament_index/resource_index/parent_prefix_path/terrain_analysis\n'}
[0.153678] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/environment/ament_prefix_path.sh\n'}
[0.153710] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/environment/ament_prefix_path.dsv\n'}
[0.153742] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/environment/path.sh\n'}
[0.153774] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/environment/path.dsv\n'}
[0.153805] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/local_setup.bash\n'}
[0.153838] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/local_setup.sh\n'}
[0.153871] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/local_setup.zsh\n'}
[0.153912] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/local_setup.dsv\n'}
[0.153944] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.dsv\n'}
[0.153977] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/ament_index/resource_index/packages/terrain_analysis\n'}
[0.154033] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/cmake/terrain_analysisConfig.cmake\n'}
[0.154066] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/cmake/terrain_analysisConfig-version.cmake\n'}
[0.154099] (terrain_analysis) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.xml\n'}
[0.154131] (terrain_analysis_ext) JobProgress: {'identifier': 'terrain_analysis_ext', 'progress': 'install'}
[0.154146] (terrain_analysis_ext) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.154448] (loam_interface) CommandEnded: {'returncode': 0}
[0.154985] (waypoint_example) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.155134] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib/waypoint_example/waypointExample\n'}
[0.155192] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/launch\n'}
[0.155245] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/launch/waypoint_example_garage.launch\n'}
[0.155308] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/data\n'}
[0.155359] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/data/waypoints_garage.ply\n'}
[0.155406] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/data/boundary_garage.ply\n'}
[0.155450] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/ament_index/resource_index/package_run_dependencies/waypoint_example\n'}
[0.155486] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/ament_index/resource_index/parent_prefix_path/waypoint_example\n'}
[0.155519] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/environment/ament_prefix_path.sh\n'}
[0.155551] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/environment/ament_prefix_path.dsv\n'}
[0.155582] (waypoint_rviz_plugin) StdoutLine: {'line': b'[100%] Built target waypoint_rviz_plugin\n'}
[0.155967] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/environment/path.sh\n'}
[0.156642] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/environment/path.dsv\n'}
[0.156787] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/local_setup.bash\n'}
[0.156884] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/local_setup.sh\n'}
[0.156975] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/local_setup.zsh\n'}
[0.157192] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/local_setup.dsv\n'}
[0.157472] (waypoint_example) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.dsv\n'}
[0.157705] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/ament_index/resource_index/packages/waypoint_example\n'}
[0.157797] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/cmake/waypoint_exampleConfig.cmake\n'}
[0.157882] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/cmake/waypoint_exampleConfig-version.cmake\n'}
[0.157967] (waypoint_example) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.xml\n'}
[0.158051] (terrain_analysis_ext) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.158383] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib/terrain_analysis_ext/terrainAnalysisExt\n'}
[0.158441] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/launch\n'}
[0.158496] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/launch/terrain_analysis_ext.launch\n'}
[0.158540] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/ament_index/resource_index/package_run_dependencies/terrain_analysis_ext\n'}
[0.158612] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/ament_index/resource_index/parent_prefix_path/terrain_analysis_ext\n'}
[0.158646] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/ament_prefix_path.sh\n'}
[0.158691] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/ament_prefix_path.dsv\n'}
[0.158719] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/path.sh\n'}
[0.158750] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/environment/path.dsv\n'}
[0.158782] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.bash\n'}
[0.158845] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.sh\n'}
[0.158881] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.zsh\n'}
[0.158913] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/local_setup.dsv\n'}
[0.158943] (terrain_analysis_ext) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.dsv\n'}
[0.158975] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/ament_index/resource_index/packages/terrain_analysis_ext\n'}
[0.159185] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/cmake/terrain_analysis_extConfig.cmake\n'}
[0.159218] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/cmake/terrain_analysis_extConfig-version.cmake\n'}
[0.159249] (terrain_analysis_ext) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.xml\n'}
[0.166407] (loam_interface) JobEnded: {'identifier': 'loam_interface', 'rc': 0}
[0.167656] (sensor_scan_generation) CommandEnded: {'returncode': 0}
[0.174840] (sensor_scan_generation) JobEnded: {'identifier': 'sensor_scan_generation', 'rc': 0}
[0.175582] (terrain_analysis_ext) CommandEnded: {'returncode': 0}
[0.182236] (terrain_analysis_ext) JobEnded: {'identifier': 'terrain_analysis_ext', 'rc': 0}
[0.182766] (terrain_analysis) CommandEnded: {'returncode': 0}
[0.189352] (terrain_analysis) JobEnded: {'identifier': 'terrain_analysis', 'rc': 0}
[0.189909] (waypoint_example) CommandEnded: {'returncode': 0}
[0.196432] (waypoint_example) JobEnded: {'identifier': 'waypoint_example', 'rc': 0}
[0.196778] (waypoint_rviz_plugin) CommandEnded: {'returncode': 0}
[0.197140] (waypoint_rviz_plugin) JobProgress: {'identifier': 'waypoint_rviz_plugin', 'progress': 'install'}
[0.197158] (waypoint_rviz_plugin) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.200581] (-) TimerEvent: {}
[0.204375] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.204498] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/plugin_description.xml\n'}
[0.204567] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib/waypoint_rviz_plugin/libwaypoint_rviz_plugin.so\n'}
[0.204641] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/ament_index/resource_index/package_run_dependencies/waypoint_rviz_plugin\n'}
[0.204700] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/ament_index/resource_index/parent_prefix_path/waypoint_rviz_plugin\n'}
[0.204821] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/ament_prefix_path.sh\n'}
[0.204901] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/ament_prefix_path.dsv\n'}
[0.204945] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/path.sh\n'}
[0.204984] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/environment/path.dsv\n'}
[0.205022] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.bash\n'}
[0.205116] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.sh\n'}
[0.205157] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.zsh\n'}
[0.205195] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/local_setup.dsv\n'}
[0.205232] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.dsv\n'}
[0.205298] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/ament_index/resource_index/packages/waypoint_rviz_plugin\n'}
[0.205334] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/ament_index/resource_index/rviz_common__pluginlib__plugin/waypoint_rviz_plugin\n'}
[0.205363] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[0.205392] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/cmake/waypoint_rviz_pluginConfig.cmake\n'}
[0.205419] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/cmake/waypoint_rviz_pluginConfig-version.cmake\n'}
[0.205446] (waypoint_rviz_plugin) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.xml\n'}
[0.206626] (waypoint_rviz_plugin) CommandEnded: {'returncode': 0}
[0.215056] (waypoint_rviz_plugin) JobEnded: {'identifier': 'waypoint_rviz_plugin', 'rc': 0}
[0.276458] (visualization_tools) StdoutLine: {'line': b'running egg_info\n'}
[0.276978] (visualization_tools) StdoutLine: {'line': b'writing visualization_tools.egg-info/PKG-INFO\n'}
[0.277130] (visualization_tools) StdoutLine: {'line': b'writing dependency_links to visualization_tools.egg-info/dependency_links.txt\n'}
[0.277211] (visualization_tools) StdoutLine: {'line': b'writing top-level names to visualization_tools.egg-info/top_level.txt\n'}
[0.278658] (visualization_tools) StdoutLine: {'line': b"reading manifest file 'visualization_tools.egg-info/SOURCES.txt'\n"}
[0.278966] (visualization_tools) StdoutLine: {'line': b"writing manifest file 'visualization_tools.egg-info/SOURCES.txt'\n"}
[0.298648] (visualization_tools) StdoutLine: {'line': b'[100%] Built target ament_cmake_python_build_visualization_tools_egg\n'}
[0.300698] (-) TimerEvent: {}
[0.307749] (visualization_tools) CommandEnded: {'returncode': 0}
[0.308285] (visualization_tools) JobProgress: {'identifier': 'visualization_tools', 'progress': 'install'}
[0.308597] (visualization_tools) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.316851] (visualization_tools) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.316995] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/visualization_tools/visualizationTools\n'}
[0.317076] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/launch\n'}
[0.317159] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/launch/visualization_tools.launch\n'}
[0.317212] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/pythonpath.sh\n'}
[0.317264] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/pythonpath.dsv\n'}
[0.317345] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info\n'}
[0.317396] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/dependency_links.txt\n'}
[0.317443] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/SOURCES.txt\n'}
[0.317491] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/top_level.txt\n'}
[0.317555] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/PKG-INFO\n'}
[0.317627] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools\n'}
[0.317668] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools/__init__.py\n'}
[0.339302] (visualization_tools) StdoutLine: {'line': b"Listing '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools'...\n"}
[0.342377] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/visualization_tools/realTimePlot.py\n'}
[0.342555] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/ament_index/resource_index/package_run_dependencies/visualization_tools\n'}
[0.342599] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/ament_index/resource_index/parent_prefix_path/visualization_tools\n'}
[0.342634] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/ament_prefix_path.sh\n'}
[0.342667] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/ament_prefix_path.dsv\n'}
[0.342698] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/path.sh\n'}
[0.342765] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/path.dsv\n'}
[0.342800] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/local_setup.bash\n'}
[0.342832] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/local_setup.sh\n'}
[0.342880] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/local_setup.zsh\n'}
[0.342908] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/local_setup.dsv\n'}
[0.342937] (visualization_tools) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.dsv\n'}
[0.342966] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/ament_index/resource_index/packages/visualization_tools\n'}
[0.342999] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/cmake/visualization_toolsConfig.cmake\n'}
[0.343026] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/cmake/visualization_toolsConfig-version.cmake\n'}
[0.343055] (visualization_tools) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.xml\n'}
[0.344701] (visualization_tools) CommandEnded: {'returncode': 0}
[0.351909] (visualization_tools) JobEnded: {'identifier': 'visualization_tools', 'rc': 0}
[0.400809] (-) TimerEvent: {}
[0.501124] (-) TimerEvent: {}
[0.601407] (-) TimerEvent: {}
[0.701685] (-) TimerEvent: {}
[0.802051] (-) TimerEvent: {}
[0.902309] (-) TimerEvent: {}
[1.002593] (-) TimerEvent: {}
[1.102872] (-) TimerEvent: {}
[1.203196] (-) TimerEvent: {}
[1.303506] (-) TimerEvent: {}
[1.403816] (-) TimerEvent: {}
[1.504131] (-) TimerEvent: {}
[1.604427] (-) TimerEvent: {}
[1.704740] (-) TimerEvent: {}
[1.805067] (-) TimerEvent: {}
[1.905366] (-) TimerEvent: {}
[2.005664] (-) TimerEvent: {}
[2.105988] (-) TimerEvent: {}
[2.206355] (-) TimerEvent: {}
[2.306670] (-) TimerEvent: {}
[2.406970] (-) TimerEvent: {}
[2.507329] (-) TimerEvent: {}
[2.607620] (-) TimerEvent: {}
[2.707905] (-) TimerEvent: {}
[2.808191] (-) TimerEvent: {}
[2.908472] (-) TimerEvent: {}
[3.008800] (-) TimerEvent: {}
[3.109064] (-) TimerEvent: {}
[3.209361] (-) TimerEvent: {}
[3.309658] (-) TimerEvent: {}
[3.409945] (-) TimerEvent: {}
[3.510347] (-) TimerEvent: {}
[3.610659] (-) TimerEvent: {}
[3.711118] (-) TimerEvent: {}
[3.811449] (-) TimerEvent: {}
[3.911778] (-) TimerEvent: {}
[4.012072] (-) TimerEvent: {}
[4.112449] (-) TimerEvent: {}
[4.212838] (-) TimerEvent: {}
[4.313263] (-) TimerEvent: {}
[4.413716] (-) TimerEvent: {}
[4.514120] (-) TimerEvent: {}
[4.614408] (-) TimerEvent: {}
[4.714694] (-) TimerEvent: {}
[4.815000] (-) TimerEvent: {}
[4.915309] (-) TimerEvent: {}
[5.015612] (-) TimerEvent: {}
[5.115932] (-) TimerEvent: {}
[5.216199] (-) TimerEvent: {}
[5.316451] (-) TimerEvent: {}
[5.416808] (-) TimerEvent: {}
[5.517059] (-) TimerEvent: {}
[5.617405] (-) TimerEvent: {}
[5.717639] (-) TimerEvent: {}
[5.818006] (-) TimerEvent: {}
[5.918372] (-) TimerEvent: {}
[6.018750] (-) TimerEvent: {}
[6.119113] (-) TimerEvent: {}
[6.219484] (-) TimerEvent: {}
[6.319819] (-) TimerEvent: {}
[6.420175] (-) TimerEvent: {}
[6.520404] (-) TimerEvent: {}
[6.620742] (-) TimerEvent: {}
[6.721073] (-) TimerEvent: {}
[6.821411] (-) TimerEvent: {}
[6.921756] (-) TimerEvent: {}
[7.022100] (-) TimerEvent: {}
[7.122490] (-) TimerEvent: {}
[7.222843] (-) TimerEvent: {}
[7.323277] (-) TimerEvent: {}
[7.423558] (-) TimerEvent: {}
[7.523921] (-) TimerEvent: {}
[7.624376] (-) TimerEvent: {}
[7.724849] (-) TimerEvent: {}
[7.825085] (-) TimerEvent: {}
[7.925425] (-) TimerEvent: {}
[8.025658] (-) TimerEvent: {}
[8.125890] (-) TimerEvent: {}
[8.226232] (-) TimerEvent: {}
[8.326476] (-) TimerEvent: {}
[8.426722] (-) TimerEvent: {}
[8.527064] (-) TimerEvent: {}
[8.627449] (-) TimerEvent: {}
[8.727818] (-) TimerEvent: {}
[8.828164] (-) TimerEvent: {}
[8.928525] (-) TimerEvent: {}
[9.028917] (-) TimerEvent: {}
[9.129287] (-) TimerEvent: {}
[9.229649] (-) TimerEvent: {}
[9.330020] (-) TimerEvent: {}
[9.430394] (-) TimerEvent: {}
[9.530750] (-) TimerEvent: {}
[9.631113] (-) TimerEvent: {}
[9.731478] (-) TimerEvent: {}
[9.831840] (-) TimerEvent: {}
[9.932195] (-) TimerEvent: {}
[10.032563] (-) TimerEvent: {}
[10.132918] (-) TimerEvent: {}
[10.233290] (-) TimerEvent: {}
[10.333561] (-) TimerEvent: {}
[10.433938] (-) TimerEvent: {}
[10.534193] (-) TimerEvent: {}
[10.634466] (-) TimerEvent: {}
[10.734761] (-) TimerEvent: {}
[10.835051] (-) TimerEvent: {}
[10.935362] (-) TimerEvent: {}
[11.035635] (-) TimerEvent: {}
[11.135965] (-) TimerEvent: {}
[11.236209] (-) TimerEvent: {}
[11.336450] (-) TimerEvent: {}
[11.436847] (-) TimerEvent: {}
[11.537239] (-) TimerEvent: {}
[11.637492] (-) TimerEvent: {}
[11.737752] (-) TimerEvent: {}
[11.837992] (-) TimerEvent: {}
[11.938271] (-) TimerEvent: {}
[12.038543] (-) TimerEvent: {}
[12.138852] (-) TimerEvent: {}
[12.239168] (-) TimerEvent: {}
[12.339487] (-) TimerEvent: {}
[12.439760] (-) TimerEvent: {}
[12.540184] (-) TimerEvent: {}
[12.640429] (-) TimerEvent: {}
[12.740695] (-) TimerEvent: {}
[12.841001] (-) TimerEvent: {}
[12.941327] (-) TimerEvent: {}
[13.041585] (-) TimerEvent: {}
[13.141811] (-) TimerEvent: {}
[13.242044] (-) TimerEvent: {}
[13.342279] (-) TimerEvent: {}
[13.442556] (-) TimerEvent: {}
[13.542810] (-) TimerEvent: {}
[13.643084] (-) TimerEvent: {}
[13.743398] (-) TimerEvent: {}
[13.843632] (-) TimerEvent: {}
[13.943927] (-) TimerEvent: {}
[14.044293] (-) TimerEvent: {}
[14.144651] (-) TimerEvent: {}
[14.244935] (-) TimerEvent: {}
[14.345288] (-) TimerEvent: {}
[14.445645] (-) TimerEvent: {}
[14.545868] (-) TimerEvent: {}
[14.646203] (-) TimerEvent: {}
[14.746541] (-) TimerEvent: {}
[14.846797] (-) TimerEvent: {}
[14.947106] (-) TimerEvent: {}
[15.047364] (-) TimerEvent: {}
[15.147639] (-) TimerEvent: {}
[15.247969] (-) TimerEvent: {}
[15.348296] (-) TimerEvent: {}
[15.448514] (-) TimerEvent: {}
[15.548845] (-) TimerEvent: {}
[15.649169] (-) TimerEvent: {}
[15.749400] (-) TimerEvent: {}
[15.849743] (-) TimerEvent: {}
[15.950105] (-) TimerEvent: {}
[16.050427] (-) TimerEvent: {}
[16.150668] (-) TimerEvent: {}
[16.250990] (-) TimerEvent: {}
[16.351343] (-) TimerEvent: {}
[16.451735] (-) TimerEvent: {}
[16.552118] (-) TimerEvent: {}
[16.652458] (-) TimerEvent: {}
[16.752669] (-) TimerEvent: {}
[16.853004] (-) TimerEvent: {}
[16.953363] (-) TimerEvent: {}
[17.053593] (-) TimerEvent: {}
[17.153814] (-) TimerEvent: {}
[17.254058] (-) TimerEvent: {}
[17.354320] (-) TimerEvent: {}
[17.454586] (-) TimerEvent: {}
[17.554826] (-) TimerEvent: {}
[17.655130] (-) TimerEvent: {}
[17.755393] (-) TimerEvent: {}
[17.855721] (-) TimerEvent: {}
[17.956064] (-) TimerEvent: {}
[18.056455] (-) TimerEvent: {}
[18.156804] (-) TimerEvent: {}
[18.257209] (-) TimerEvent: {}
[18.357565] (-) TimerEvent: {}
[18.457931] (-) TimerEvent: {}
[18.558352] (-) TimerEvent: {}
[18.658658] (-) TimerEvent: {}
[18.758955] (-) TimerEvent: {}
[18.859418] (-) TimerEvent: {}
[18.959709] (-) TimerEvent: {}
[19.048151] (local_planner) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable localPlanner\x1b[0m\n'}
[19.059784] (-) TimerEvent: {}
[19.160051] (-) TimerEvent: {}
[19.260352] (-) TimerEvent: {}
[19.360667] (-) TimerEvent: {}
[19.461025] (-) TimerEvent: {}
[19.561456] (-) TimerEvent: {}
[19.661794] (-) TimerEvent: {}
[19.762131] (-) TimerEvent: {}
[19.862427] (-) TimerEvent: {}
[19.962779] (-) TimerEvent: {}
[20.063113] (-) TimerEvent: {}
[20.163530] (-) TimerEvent: {}
[20.263844] (-) TimerEvent: {}
[20.364241] (-) TimerEvent: {}
[20.464650] (-) TimerEvent: {}
[20.565000] (-) TimerEvent: {}
[20.665398] (-) TimerEvent: {}
[20.765769] (-) TimerEvent: {}
[20.866142] (-) TimerEvent: {}
[20.928496] (local_planner) StdoutLine: {'line': b'[100%] Built target localPlanner\n'}
[20.945201] (local_planner) CommandEnded: {'returncode': 0}
[20.946360] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'install'}
[20.947216] (local_planner) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1721'), ('SYSTEMD_EXEC_PID', '2255'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':1'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:da854a6e-52f0-4506-b2a8-40d1936bf706'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[20.959013] (local_planner) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[20.959238] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/localPlanner\n'}
[20.966259] (-) TimerEvent: {}
[20.972321] (local_planner) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/localPlanner" to ""\n'}
[20.972390] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/pathFollower\n'}
[20.972498] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch\n'}
[20.972539] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch/local_planner.launch.py\n'}
[20.972576] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch/local_planner.launch\n'}
[20.972654] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths\n'}
[20.972727] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/startPaths.ply\n'}
[20.972767] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/correspondences.txt\n'}
[20.972803] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/pathList.ply\n'}
[20.972840] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/paths.ply\n'}
[20.972877] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/path_generator.m\n'}
[20.972912] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/config\n'}
[20.972948] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/package_run_dependencies/local_planner\n'}
[20.972984] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/parent_prefix_path/local_planner\n'}
[20.973019] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/ament_prefix_path.sh\n'}
[20.973055] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/ament_prefix_path.dsv\n'}
[20.973090] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/path.sh\n'}
[20.973125] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/path.dsv\n'}
[20.973159] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.bash\n'}
[20.973194] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.sh\n'}
[20.973257] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.zsh\n'}
[20.973295] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.dsv\n'}
[20.973331] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv\n'}
[20.973365] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/packages/local_planner\n'}
[20.973401] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/cmake/local_plannerConfig.cmake\n'}
[20.973458] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/cmake/local_plannerConfig-version.cmake\n'}
[20.973540] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.xml\n'}
[20.974177] (local_planner) CommandEnded: {'returncode': 0}
[20.982521] (local_planner) JobEnded: {'identifier': 'local_planner', 'rc': 0}
[20.982944] (-) EventReactorShutdown: {}
