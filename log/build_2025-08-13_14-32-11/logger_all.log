[0.064s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.065s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7b053e258c40>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7b053e2587f0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7b053e2587f0>>)
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.163s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/saoxueche0808pm/saoxueche'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['ignore', 'ignore_ament_install']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'ignore'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'ignore_ament_install'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['colcon_pkg']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'colcon_pkg'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['colcon_meta']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'colcon_meta'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'ros'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['cmake', 'python']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'cmake'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'python'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['python_setup_py']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'python_setup_py'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['ignore', 'ignore_ament_install']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ignore'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ignore_ament_install'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['colcon_pkg']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'colcon_pkg'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['colcon_meta']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'colcon_meta'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ros'
[0.176s] DEBUG:colcon.colcon_core.package_identification:Package 'src/integrated_navigation' with type 'ros.ament_cmake' and name 'integrated_navigation'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['ignore', 'ignore_ament_install']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ignore'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ignore_ament_install'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['colcon_pkg']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'colcon_pkg'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['colcon_meta']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'colcon_meta'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['ros']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ros'
[0.177s] DEBUG:colcon.colcon_core.package_identification:Package 'src/loam_interface' with type 'ros.ament_cmake' and name 'loam_interface'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore_ament_install'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_pkg']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_pkg'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_meta']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_meta'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ros']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ros'
[0.178s] DEBUG:colcon.colcon_core.package_identification:Package 'src/local_planner' with type 'ros.ament_cmake' and name 'local_planner'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['ignore', 'ignore_ament_install']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ignore'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ignore_ament_install'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['colcon_pkg']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'colcon_pkg'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['colcon_meta']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'colcon_meta'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['ros']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ros'
[0.179s] DEBUG:colcon.colcon_core.package_identification:Package 'src/nav2_smac_test' with type 'ros.ament_cmake' and name 'nav2_smac_test'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['ignore', 'ignore_ament_install']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ignore'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ignore_ament_install'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['colcon_pkg']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'colcon_pkg'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['colcon_meta']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'colcon_meta'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['ros']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ros'
[0.180s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sensor_scan_generation' with type 'ros.ament_cmake' and name 'sensor_scan_generation'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ignore', 'ignore_ament_install']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore_ament_install'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_pkg']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_pkg'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_meta']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_meta'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ros']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ros'
[0.181s] DEBUG:colcon.colcon_core.package_identification:Package 'src/terrain_analysis' with type 'ros.ament_cmake' and name 'terrain_analysis'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['ignore', 'ignore_ament_install']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ignore'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ignore_ament_install'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['colcon_pkg']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'colcon_pkg'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['colcon_meta']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'colcon_meta'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['ros']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ros'
[0.182s] DEBUG:colcon.colcon_core.package_identification:Package 'src/terrain_analysis_ext' with type 'ros.ament_cmake' and name 'terrain_analysis_ext'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extension 'ignore'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) ignored
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'ignore'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) ignored
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['ignore', 'ignore_ament_install']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ignore'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ignore_ament_install'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['colcon_pkg']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'colcon_pkg'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['colcon_meta']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'colcon_meta'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['ros']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ros'
[0.183s] DEBUG:colcon.colcon_core.package_identification:Package 'src/visualization_tools' with type 'ros.ament_cmake' and name 'visualization_tools'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['ignore', 'ignore_ament_install']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ignore'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ignore_ament_install'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['colcon_pkg']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'colcon_pkg'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['colcon_meta']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'colcon_meta'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['ros']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ros'
[0.184s] DEBUG:colcon.colcon_core.package_identification:Package 'src/waypoint_example' with type 'ros.ament_cmake' and name 'waypoint_example'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['ignore', 'ignore_ament_install']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ignore'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ignore_ament_install'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['colcon_pkg']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'colcon_pkg'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['colcon_meta']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'colcon_meta'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['ros']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ros'
[0.185s] DEBUG:colcon.colcon_core.package_identification:Package 'src/waypoint_rviz_plugin' with type 'ros.ament_cmake' and name 'waypoint_rviz_plugin'
[0.185s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.185s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.185s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.185s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.185s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.209s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.209s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.211s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 473 installed packages in /opt/ros/humble
[0.212s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.245s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_args' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_target' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_clean_cache' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_clean_first' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_force_configure' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'ament_cmake_args' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'catkin_cmake_args' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.245s] DEBUG:colcon.colcon_core.verb:Building package 'integrated_navigation' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/integrated_navigation', 'symlink_install': False, 'test_result_base': None}
[0.245s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_args' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_target' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_clean_cache' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_clean_first' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_force_configure' from command line to 'False'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'ament_cmake_args' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'catkin_cmake_args' from command line to 'None'
[0.245s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.245s] DEBUG:colcon.colcon_core.verb:Building package 'loam_interface' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/loam_interface', 'symlink_install': False, 'test_result_base': None}
[0.246s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_args' from command line to 'None'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target' from command line to 'None'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.246s] DEBUG:colcon.colcon_core.verb:Building package 'local_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner', 'symlink_install': False, 'test_result_base': None}
[0.246s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_args' from command line to 'None'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_target' from command line to 'None'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_clean_cache' from command line to 'False'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_clean_first' from command line to 'False'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_force_configure' from command line to 'False'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'ament_cmake_args' from command line to 'None'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'catkin_cmake_args' from command line to 'None'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.246s] DEBUG:colcon.colcon_core.verb:Building package 'nav2_smac_test' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/nav2_smac_test', 'symlink_install': False, 'test_result_base': None}
[0.246s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_args' from command line to 'None'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_target' from command line to 'None'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_clean_cache' from command line to 'False'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_clean_first' from command line to 'False'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_force_configure' from command line to 'False'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'ament_cmake_args' from command line to 'None'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'catkin_cmake_args' from command line to 'None'
[0.246s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.246s] DEBUG:colcon.colcon_core.verb:Building package 'sensor_scan_generation' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/sensor_scan_generation', 'symlink_install': False, 'test_result_base': None}
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_args' from command line to 'None'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_target' from command line to 'None'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_clean_cache' from command line to 'False'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_clean_first' from command line to 'False'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_force_configure' from command line to 'False'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'ament_cmake_args' from command line to 'None'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'catkin_cmake_args' from command line to 'None'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.247s] DEBUG:colcon.colcon_core.verb:Building package 'terrain_analysis' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis', 'symlink_install': False, 'test_result_base': None}
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_args' from command line to 'None'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_target' from command line to 'None'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_clean_cache' from command line to 'False'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_clean_first' from command line to 'False'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_force_configure' from command line to 'False'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'ament_cmake_args' from command line to 'None'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'catkin_cmake_args' from command line to 'None'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.247s] DEBUG:colcon.colcon_core.verb:Building package 'terrain_analysis_ext' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis_ext', 'symlink_install': False, 'test_result_base': None}
[0.247s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_args' from command line to 'None'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_target' from command line to 'None'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_clean_cache' from command line to 'False'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_clean_first' from command line to 'False'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_force_configure' from command line to 'False'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'ament_cmake_args' from command line to 'None'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'catkin_cmake_args' from command line to 'None'
[0.247s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.247s] DEBUG:colcon.colcon_core.verb:Building package 'visualization_tools' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/visualization_tools', 'symlink_install': False, 'test_result_base': None}
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_target' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_clean_cache' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_clean_first' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_force_configure' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'ament_cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'catkin_cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.248s] DEBUG:colcon.colcon_core.verb:Building package 'waypoint_example' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_example', 'symlink_install': False, 'test_result_base': None}
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_target' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_clean_cache' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_clean_first' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_force_configure' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'ament_cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'catkin_cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.248s] DEBUG:colcon.colcon_core.verb:Building package 'waypoint_rviz_plugin' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_rviz_plugin', 'symlink_install': False, 'test_result_base': None}
[0.248s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.249s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.249s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/integrated_navigation' with build type 'ament_cmake'
[0.249s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/integrated_navigation'
[0.251s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.251s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.251s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.254s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/loam_interface' with build type 'ament_cmake'
[0.254s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/loam_interface'
[0.254s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.254s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.256s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner' with build type 'ament_cmake'
[0.256s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner'
[0.257s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.257s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.259s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/nav2_smac_test' with build type 'ament_cmake'
[0.259s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/nav2_smac_test'
[0.259s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.259s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.261s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/sensor_scan_generation' with build type 'ament_cmake'
[0.261s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/sensor_scan_generation'
[0.261s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.261s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.263s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis' with build type 'ament_cmake'
[0.263s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis'
[0.263s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.263s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.265s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis_ext' with build type 'ament_cmake'
[0.265s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis_ext'
[0.265s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.265s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.267s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/visualization_tools' with build type 'ament_cmake'
[0.267s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/visualization_tools'
[0.267s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.268s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.269s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_example' with build type 'ament_cmake'
[0.270s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_example'
[0.270s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.270s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.272s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_rviz_plugin' with build type 'ament_cmake'
[0.272s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_rviz_plugin'
[0.272s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.272s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.278s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation -- -j16 -l16
[0.281s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface -- -j16 -l16
[0.283s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner -- -j16 -l16
[0.285s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test -- -j16 -l16
[0.291s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation -- -j16 -l16
[0.296s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis -- -j16 -l16
[0.301s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext -- -j16 -l16
[0.307s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools -- -j16 -l16
[0.314s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example -- -j16 -l16
[0.324s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin -- -j16 -l16
[0.326s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation -- -j16 -l16
[0.336s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation
[0.338s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test -- -j16 -l16
[0.340s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test
[0.348s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(integrated_navigation)
[0.349s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation
[0.352s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation' for CMake module files
[0.352s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation' for CMake config files
[0.352s] Level 1:colcon.colcon_core.shell:create_environment_hook('integrated_navigation', 'cmake_prefix_path')
[0.353s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.ps1'
[0.353s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.dsv'
[0.354s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.sh'
[0.356s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/bin'
[0.356s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/lib/pkgconfig/integrated_navigation.pc'
[0.356s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/lib/python3.10/site-packages'
[0.356s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/bin'
[0.357s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.ps1'
[0.357s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.dsv'
[0.358s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.sh'
[0.358s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.bash'
[0.359s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.zsh'
[0.359s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/colcon-core/packages/integrated_navigation)
[0.360s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(integrated_navigation)
[0.360s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation' for CMake module files
[0.360s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation' for CMake config files
[0.360s] Level 1:colcon.colcon_core.shell:create_environment_hook('integrated_navigation', 'cmake_prefix_path')
[0.361s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.ps1'
[0.361s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.dsv'
[0.362s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.sh'
[0.362s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/bin'
[0.362s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/lib/pkgconfig/integrated_navigation.pc'
[0.363s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/lib/python3.10/site-packages'
[0.363s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/bin'
[0.363s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.ps1'
[0.364s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.dsv'
[0.364s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.sh'
[0.364s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.bash'
[0.365s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.zsh'
[0.365s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/colcon-core/packages/integrated_navigation)
[0.366s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(nav2_smac_test)
[0.367s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test' for CMake module files
[0.368s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test
[0.368s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test' for CMake config files
[0.368s] Level 1:colcon.colcon_core.shell:create_environment_hook('nav2_smac_test', 'cmake_prefix_path')
[0.368s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.ps1'
[0.369s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.dsv'
[0.369s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.sh'
[0.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/bin'
[0.371s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/lib/pkgconfig/nav2_smac_test.pc'
[0.371s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/lib/python3.10/site-packages'
[0.371s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/bin'
[0.371s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.ps1'
[0.372s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.dsv'
[0.372s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.sh'
[0.373s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.bash'
[0.373s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.zsh'
[0.374s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/colcon-core/packages/nav2_smac_test)
[0.375s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(nav2_smac_test)
[0.375s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test' for CMake module files
[0.376s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test' for CMake config files
[0.376s] Level 1:colcon.colcon_core.shell:create_environment_hook('nav2_smac_test', 'cmake_prefix_path')
[0.376s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.ps1'
[0.377s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.dsv'
[0.378s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.sh'
[0.378s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/bin'
[0.379s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/lib/pkgconfig/nav2_smac_test.pc'
[0.379s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/lib/python3.10/site-packages'
[0.382s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/bin'
[0.382s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.ps1'
[0.383s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.dsv'
[0.384s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.sh'
[0.384s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.bash'
[0.385s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/nav2_smac_test/package.zsh'
[0.385s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test/share/colcon-core/packages/nav2_smac_test)
[0.387s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface -- -j16 -l16
[0.389s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface
[0.390s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis -- -j16 -l16
[0.391s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis
[0.392s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation -- -j16 -l16
[0.393s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation
[0.393s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example -- -j16 -l16
[0.395s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example
[0.399s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext -- -j16 -l16
[0.400s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(loam_interface)
[0.400s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface' for CMake module files
[0.402s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface' for CMake config files
[0.403s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext
[0.403s] Level 1:colcon.colcon_core.shell:create_environment_hook('loam_interface', 'cmake_prefix_path')
[0.404s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface
[0.404s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.ps1'
[0.406s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.dsv'
[0.408s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.sh'
[0.409s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib'
[0.409s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/bin'
[0.409s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib/pkgconfig/loam_interface.pc'
[0.410s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib/python3.10/site-packages'
[0.410s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/bin'
[0.410s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.ps1'
[0.411s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.dsv'
[0.411s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.sh'
[0.411s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.bash'
[0.411s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.zsh'
[0.412s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/colcon-core/packages/loam_interface)
[0.412s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(loam_interface)
[0.412s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface' for CMake module files
[0.412s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface' for CMake config files
[0.412s] Level 1:colcon.colcon_core.shell:create_environment_hook('loam_interface', 'cmake_prefix_path')
[0.413s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.ps1'
[0.413s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.dsv'
[0.413s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.sh'
[0.413s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib'
[0.413s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/bin'
[0.413s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib/pkgconfig/loam_interface.pc'
[0.414s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib/python3.10/site-packages'
[0.414s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/bin'
[0.414s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.ps1'
[0.414s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.dsv'
[0.415s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.sh'
[0.415s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.bash'
[0.415s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.zsh'
[0.415s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/colcon-core/packages/loam_interface)
[0.417s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sensor_scan_generation)
[0.417s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation' for CMake module files
[0.417s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation
[0.417s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation' for CMake config files
[0.417s] Level 1:colcon.colcon_core.shell:create_environment_hook('sensor_scan_generation', 'cmake_prefix_path')
[0.417s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.ps1'
[0.418s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.dsv'
[0.418s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.sh'
[0.418s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib'
[0.418s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/bin'
[0.418s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib/pkgconfig/sensor_scan_generation.pc'
[0.419s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib/python3.10/site-packages'
[0.419s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/bin'
[0.419s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.ps1'
[0.419s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv'
[0.419s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.sh'
[0.420s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.bash'
[0.420s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.zsh'
[0.420s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/colcon-core/packages/sensor_scan_generation)
[0.420s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sensor_scan_generation)
[0.420s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation' for CMake module files
[0.421s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation' for CMake config files
[0.421s] Level 1:colcon.colcon_core.shell:create_environment_hook('sensor_scan_generation', 'cmake_prefix_path')
[0.421s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.ps1'
[0.421s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.dsv'
[0.421s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.sh'
[0.422s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib'
[0.422s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/bin'
[0.422s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib/pkgconfig/sensor_scan_generation.pc'
[0.422s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib/python3.10/site-packages'
[0.422s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/bin'
[0.422s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.ps1'
[0.422s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv'
[0.423s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.sh'
[0.423s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.bash'
[0.423s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.zsh'
[0.423s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/colcon-core/packages/sensor_scan_generation)
[0.424s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis_ext)
[0.424s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext' for CMake module files
[0.424s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext' for CMake config files
[0.425s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext
[0.425s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis_ext', 'cmake_prefix_path')
[0.425s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.ps1'
[0.425s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.dsv'
[0.426s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.sh'
[0.426s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib'
[0.426s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/bin'
[0.426s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib/pkgconfig/terrain_analysis_ext.pc'
[0.426s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib/python3.10/site-packages'
[0.426s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/bin'
[0.426s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.ps1'
[0.427s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.dsv'
[0.427s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.sh'
[0.427s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.bash'
[0.427s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.zsh'
[0.428s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/colcon-core/packages/terrain_analysis_ext)
[0.428s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis_ext)
[0.428s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext' for CMake module files
[0.428s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext' for CMake config files
[0.428s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis_ext', 'cmake_prefix_path')
[0.429s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.ps1'
[0.429s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.dsv'
[0.429s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.sh'
[0.429s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib'
[0.429s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/bin'
[0.429s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib/pkgconfig/terrain_analysis_ext.pc'
[0.430s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib/python3.10/site-packages'
[0.430s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/bin'
[0.430s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.ps1'
[0.430s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.dsv'
[0.430s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.sh'
[0.431s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.bash'
[0.431s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.zsh'
[0.431s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/colcon-core/packages/terrain_analysis_ext)
[0.431s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis)
[0.431s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis' for CMake module files
[0.432s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis
[0.432s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis' for CMake config files
[0.432s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis', 'cmake_prefix_path')
[0.432s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.ps1'
[0.432s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.dsv'
[0.433s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.sh'
[0.433s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib'
[0.433s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/bin'
[0.433s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib/pkgconfig/terrain_analysis.pc'
[0.433s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib/python3.10/site-packages'
[0.433s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/bin'
[0.433s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.ps1'
[0.434s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.dsv'
[0.434s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.sh'
[0.434s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.bash'
[0.434s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.zsh'
[0.435s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/colcon-core/packages/terrain_analysis)
[0.435s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis)
[0.435s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis' for CMake module files
[0.435s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis' for CMake config files
[0.435s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis', 'cmake_prefix_path')
[0.435s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.ps1'
[0.436s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.dsv'
[0.436s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.sh'
[0.436s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib'
[0.436s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/bin'
[0.436s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib/pkgconfig/terrain_analysis.pc'
[0.436s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib/python3.10/site-packages'
[0.436s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/bin'
[0.437s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.ps1'
[0.437s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.dsv'
[0.437s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.sh'
[0.438s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.bash'
[0.438s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.zsh'
[0.438s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/colcon-core/packages/terrain_analysis)
[0.438s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_example)
[0.438s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example' for CMake module files
[0.439s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example
[0.439s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example' for CMake config files
[0.439s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_example', 'cmake_prefix_path')
[0.439s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.ps1'
[0.440s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.dsv'
[0.440s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.sh'
[0.440s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib'
[0.440s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/bin'
[0.440s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib/pkgconfig/waypoint_example.pc'
[0.440s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib/python3.10/site-packages'
[0.440s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/bin'
[0.441s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.ps1'
[0.441s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.dsv'
[0.441s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.sh'
[0.442s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.bash'
[0.442s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.zsh'
[0.442s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/colcon-core/packages/waypoint_example)
[0.442s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_example)
[0.442s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example' for CMake module files
[0.443s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example' for CMake config files
[0.443s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_example', 'cmake_prefix_path')
[0.443s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.ps1'
[0.443s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.dsv'
[0.443s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.sh'
[0.444s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib'
[0.444s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/bin'
[0.444s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib/pkgconfig/waypoint_example.pc'
[0.444s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib/python3.10/site-packages'
[0.444s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/bin'
[0.444s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.ps1'
[0.444s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.dsv'
[0.445s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.sh'
[0.445s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.bash'
[0.445s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.zsh'
[0.445s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/colcon-core/packages/waypoint_example)
[0.446s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin -- -j16 -l16
[0.447s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin
[0.455s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_rviz_plugin)
[0.456s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin
[0.456s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin' for CMake module files
[0.457s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin' for CMake config files
[0.457s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_rviz_plugin', 'cmake_prefix_path')
[0.457s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.ps1'
[0.458s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.dsv'
[0.458s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.sh'
[0.458s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib'
[0.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/bin'
[0.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib/pkgconfig/waypoint_rviz_plugin.pc'
[0.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib/python3.10/site-packages'
[0.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/bin'
[0.459s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.ps1'
[0.459s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.dsv'
[0.460s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.sh'
[0.460s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.bash'
[0.460s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.zsh'
[0.460s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/colcon-core/packages/waypoint_rviz_plugin)
[0.461s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_rviz_plugin)
[0.461s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin' for CMake module files
[0.461s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin' for CMake config files
[0.461s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_rviz_plugin', 'cmake_prefix_path')
[0.461s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.ps1'
[0.462s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.dsv'
[0.462s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.sh'
[0.462s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib'
[0.462s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/bin'
[0.462s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib/pkgconfig/waypoint_rviz_plugin.pc'
[0.462s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib/python3.10/site-packages'
[0.462s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/bin'
[0.463s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.ps1'
[0.463s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.dsv'
[0.463s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.sh'
[0.463s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.bash'
[0.464s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.zsh'
[0.464s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/colcon-core/packages/waypoint_rviz_plugin)
[0.557s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools -- -j16 -l16
[0.558s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools
[0.594s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(visualization_tools)
[0.594s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools' for CMake module files
[0.594s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools
[0.594s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools' for CMake config files
[0.595s] Level 1:colcon.colcon_core.shell:create_environment_hook('visualization_tools', 'cmake_prefix_path')
[0.595s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.ps1'
[0.595s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.dsv'
[0.595s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.sh'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/bin'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/pkgconfig/visualization_tools.pc'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/python3.10/site-packages'
[0.596s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/bin'
[0.596s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.ps1'
[0.596s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.dsv'
[0.597s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.sh'
[0.597s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.bash'
[0.597s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.zsh'
[0.597s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/colcon-core/packages/visualization_tools)
[0.598s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(visualization_tools)
[0.598s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools' for CMake module files
[0.598s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools' for CMake config files
[0.598s] Level 1:colcon.colcon_core.shell:create_environment_hook('visualization_tools', 'cmake_prefix_path')
[0.598s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.ps1'
[0.598s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.dsv'
[0.599s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.sh'
[0.599s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib'
[0.599s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/bin'
[0.599s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/pkgconfig/visualization_tools.pc'
[0.599s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/python3.10/site-packages'
[0.599s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/bin'
[0.599s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.ps1'
[0.600s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.dsv'
[0.600s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.sh'
[0.600s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.bash'
[0.600s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.zsh'
[0.601s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/colcon-core/packages/visualization_tools)
[21.195s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner -- -j16 -l16
[21.199s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner
[21.223s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[21.223s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner
[21.224s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake module files
[21.224s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake config files
[21.224s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[21.224s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[21.225s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[21.225s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[21.225s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib'
[21.226s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[21.226s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/pkgconfig/local_planner.pc'
[21.226s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/python3.10/site-packages'
[21.226s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[21.226s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.ps1'
[21.227s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv'
[21.227s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.sh'
[21.227s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.bash'
[21.227s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.zsh'
[21.228s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/colcon-core/packages/local_planner)
[21.228s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[21.228s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake module files
[21.228s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake config files
[21.228s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[21.229s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[21.229s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[21.229s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[21.229s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib'
[21.229s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[21.230s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/pkgconfig/local_planner.pc'
[21.230s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/python3.10/site-packages'
[21.230s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[21.230s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.ps1'
[21.230s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv'
[21.231s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.sh'
[21.231s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.bash'
[21.231s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.zsh'
[21.231s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/colcon-core/packages/local_planner)
[21.231s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[21.232s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[21.232s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[21.232s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[21.235s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[21.235s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[21.236s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[21.242s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[21.243s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.ps1'
[21.243s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/saoxueche0808pm/saoxueche/install/_local_setup_util_ps1.py'
[21.245s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.ps1'
[21.245s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.sh'
[21.245s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/saoxueche0808pm/saoxueche/install/_local_setup_util_sh.py'
[21.246s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.sh'
[21.246s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.bash'
[21.247s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.bash'
[21.247s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.zsh'
[21.247s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.zsh'
