[0.039s] Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools -- -j16 -l16
[0.100s] [  0%] Built target ament_cmake_python_copy_visualization_tools
[0.110s] [100%] Built target visualizationTools
[0.258s] running egg_info
[0.258s] writing visualization_tools.egg-info/PKG-INFO
[0.259s] writing dependency_links to visualization_tools.egg-info/dependency_links.txt
[0.259s] writing top-level names to visualization_tools.egg-info/top_level.txt
[0.260s] reading manifest file 'visualization_tools.egg-info/SOURCES.txt'
[0.260s] writing manifest file 'visualization_tools.egg-info/SOURCES.txt'
[0.280s] [100%] Built target ament_cmake_python_build_visualization_tools_egg
[0.289s] Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools -- -j16 -l16
[0.291s] Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools
[0.298s] -- Install configuration: ""
[0.298s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/visualization_tools/visualizationTools
[0.299s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/launch
[0.299s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/launch/visualization_tools.launch
[0.299s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/pythonpath.sh
[0.299s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/pythonpath.dsv
[0.299s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info
[0.299s] -- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/dependency_links.txt
[0.299s] -- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/SOURCES.txt
[0.299s] -- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/top_level.txt
[0.299s] -- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info/PKG-INFO
[0.299s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools
[0.299s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools/__init__.py
[0.321s] Listing '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/local/lib/python3.10/dist-packages/visualization_tools'...
[0.324s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/visualization_tools/realTimePlot.py
[0.324s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/ament_index/resource_index/package_run_dependencies/visualization_tools
[0.324s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/ament_index/resource_index/parent_prefix_path/visualization_tools
[0.324s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/ament_prefix_path.sh
[0.324s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/ament_prefix_path.dsv
[0.324s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/path.sh
[0.324s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/environment/path.dsv
[0.324s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/local_setup.bash
[0.324s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/local_setup.sh
[0.324s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/local_setup.zsh
[0.324s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/local_setup.dsv
[0.324s] -- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.dsv
[0.324s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/ament_index/resource_index/packages/visualization_tools
[0.324s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/cmake/visualization_toolsConfig.cmake
[0.325s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/cmake/visualization_toolsConfig-version.cmake
[0.325s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.xml
[0.327s] Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools
