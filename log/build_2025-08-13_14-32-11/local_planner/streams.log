[0.026s] Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner -- -j16 -l16
[0.104s] [ 25%] [32mBuilding CXX object CMakeFiles/localPlanner.dir/src/localPlanner.cpp.o[0m
[0.105s] [ 75%] Built target pathFollower
[19.040s] [100%] [32m[1mLinking CXX executable localPlanner[0m
[20.921s] [100%] Built target localPlanner
[20.938s] Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner -- -j16 -l16
[20.942s] Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner
[20.951s] -- Install configuration: ""
[20.952s] -- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/localPlanner
[20.965s] -- Set runtime path of "/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/localPlanner" to ""
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/pathFollower
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch/local_planner.launch.py
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch/local_planner.launch
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/startPaths.ply
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/correspondences.txt
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/pathList.ply
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/paths.ply
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/path_generator.m
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/config
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/package_run_dependencies/local_planner
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/parent_prefix_path/local_planner
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/ament_prefix_path.sh
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/ament_prefix_path.dsv
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/path.sh
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/path.dsv
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.bash
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.sh
[20.965s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.zsh
[20.966s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.dsv
[20.966s] -- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv
[20.966s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/packages/local_planner
[20.966s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/cmake/local_plannerConfig.cmake
[20.966s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/cmake/local_plannerConfig-version.cmake
[20.966s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.xml
[20.966s] Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner
