[0.000000] (-) TimerEvent: {}
[0.000290] (-) JobUnselected: {'identifier': 'integrated_navigation'}
[0.000775] (-) JobUnselected: {'identifier': 'loam_interface'}
[0.000931] (-) JobUnselected: {'identifier': 'nav2_smac_test'}
[0.001050] (-) JobUnselected: {'identifier': 'sensor_scan_generation'}
[0.001120] (-) JobUnselected: {'identifier': 'terrain_analysis'}
[0.001161] (-) JobUnselected: {'identifier': 'terrain_analysis_ext'}
[0.001194] (-) JobUnselected: {'identifier': 'visualization_tools'}
[0.001226] (-) JobUnselected: {'identifier': 'waypoint_example'}
[0.001256] (-) JobUnselected: {'identifier': 'waypoint_rviz_plugin'}
[0.001291] (local_planner) JobQueued: {'identifier': 'local_planner', 'dependencies': OrderedDict()}
[0.001329] (local_planner) JobStarted: {'identifier': 'local_planner'}
[0.011000] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'cmake'}
[0.011635] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'build'}
[0.012259] (local_planner) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('CLAUDE_CODE_ENTRYPOINT', 'cli'), ('LC_TIME', 'zh_CN.UTF-8'), ('GIT_EDITOR', 'true'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1721'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2255'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '55520'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('ANTHROPIC_BASE_URL', 'https://gaccodeapi.com'), ('TERM', 'xterm-256color'), ('OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE', 'delta'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('ANTHROPIC_API_KEY', 'sk-hHAkxaMgu7ISI42NI97HrlnSmZkSYwI4fkMk3xKdHp6QDldQ'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('COREPACK_ENABLE_AUTO_PIN', '0'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-1a39ff7581ade6bb.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dcb10a45d5.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:6ae87ae5-7baf-4b26-89ab-8d76be68d48f'), ('ANTHROPIC_AUTH_TOKEN', 'sk-hHAkxaMgu7ISI42NI97HrlnSmZkSYwI4fkMk3xKdHp6QDldQ'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('CLAUDECODE', '1'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.068231] (local_planner) StdoutLine: {'line': b'Consolidate compiler generated dependencies of target pathFollower\n'}
[0.068471] (local_planner) StdoutLine: {'line': b'Consolidate compiler generated dependencies of target localPlanner\n'}
[0.084471] (local_planner) StdoutLine: {'line': b'[ 50%] Built target pathFollower\n'}
[0.086113] (local_planner) StdoutLine: {'line': b'[ 75%] Building CXX object CMakeFiles/localPlanner.dir/src/localPlanner.cpp.o\n'}
[0.099646] (-) TimerEvent: {}
[0.199820] (-) TimerEvent: {}
[0.300033] (-) TimerEvent: {}
[0.400238] (-) TimerEvent: {}
[0.500537] (-) TimerEvent: {}
[0.600765] (-) TimerEvent: {}
[0.700951] (-) TimerEvent: {}
[0.801236] (-) TimerEvent: {}
[0.901409] (-) TimerEvent: {}
[1.001635] (-) TimerEvent: {}
[1.101946] (-) TimerEvent: {}
[1.202171] (-) TimerEvent: {}
[1.302353] (-) TimerEvent: {}
[1.402584] (-) TimerEvent: {}
[1.502760] (-) TimerEvent: {}
[1.602985] (-) TimerEvent: {}
[1.703185] (-) TimerEvent: {}
[1.803395] (-) TimerEvent: {}
[1.903624] (-) TimerEvent: {}
[2.003865] (-) TimerEvent: {}
[2.104122] (-) TimerEvent: {}
[2.204308] (-) TimerEvent: {}
[2.304482] (-) TimerEvent: {}
[2.404681] (-) TimerEvent: {}
[2.504910] (-) TimerEvent: {}
[2.605151] (-) TimerEvent: {}
[2.705321] (-) TimerEvent: {}
[2.805499] (-) TimerEvent: {}
[2.905767] (-) TimerEvent: {}
[3.006049] (-) TimerEvent: {}
[3.106273] (-) TimerEvent: {}
[3.206454] (-) TimerEvent: {}
[3.306739] (-) TimerEvent: {}
[3.406918] (-) TimerEvent: {}
[3.507108] (-) TimerEvent: {}
[3.607299] (-) TimerEvent: {}
[3.707496] (-) TimerEvent: {}
[3.807702] (-) TimerEvent: {}
[3.907910] (-) TimerEvent: {}
[4.008141] (-) TimerEvent: {}
[4.108376] (-) TimerEvent: {}
[4.208602] (-) TimerEvent: {}
[4.308803] (-) TimerEvent: {}
[4.409029] (-) TimerEvent: {}
[4.509235] (-) TimerEvent: {}
[4.609431] (-) TimerEvent: {}
[4.709614] (-) TimerEvent: {}
[4.809798] (-) TimerEvent: {}
[4.909924] (-) TimerEvent: {}
[5.010210] (-) TimerEvent: {}
[5.110381] (-) TimerEvent: {}
[5.210596] (-) TimerEvent: {}
[5.310797] (-) TimerEvent: {}
[5.411062] (-) TimerEvent: {}
[5.511232] (-) TimerEvent: {}
[5.611439] (-) TimerEvent: {}
[5.711674] (-) TimerEvent: {}
[5.811961] (-) TimerEvent: {}
[5.912149] (-) TimerEvent: {}
[6.012339] (-) TimerEvent: {}
[6.112611] (-) TimerEvent: {}
[6.212879] (-) TimerEvent: {}
[6.313114] (-) TimerEvent: {}
[6.413402] (-) TimerEvent: {}
[6.513698] (-) TimerEvent: {}
[6.613917] (-) TimerEvent: {}
[6.714135] (-) TimerEvent: {}
[6.814307] (-) TimerEvent: {}
[6.914480] (-) TimerEvent: {}
[7.014725] (-) TimerEvent: {}
[7.115038] (-) TimerEvent: {}
[7.215276] (-) TimerEvent: {}
[7.315466] (-) TimerEvent: {}
[7.415682] (-) TimerEvent: {}
[7.515895] (-) TimerEvent: {}
[7.616253] (-) TimerEvent: {}
[7.716558] (-) TimerEvent: {}
[7.816762] (-) TimerEvent: {}
[7.917031] (-) TimerEvent: {}
[8.017269] (-) TimerEvent: {}
[8.117454] (-) TimerEvent: {}
[8.217692] (-) TimerEvent: {}
[8.317994] (-) TimerEvent: {}
[8.418273] (-) TimerEvent: {}
[8.518469] (-) TimerEvent: {}
[8.618675] (-) TimerEvent: {}
[8.718857] (-) TimerEvent: {}
[8.819075] (-) TimerEvent: {}
[8.919262] (-) TimerEvent: {}
[9.019440] (-) TimerEvent: {}
[9.119725] (-) TimerEvent: {}
[9.220035] (-) TimerEvent: {}
[9.320274] (-) TimerEvent: {}
[9.420509] (-) TimerEvent: {}
[9.520721] (-) TimerEvent: {}
[9.621045] (-) TimerEvent: {}
[9.721291] (-) TimerEvent: {}
[9.821471] (-) TimerEvent: {}
[9.921688] (-) TimerEvent: {}
[10.021973] (-) TimerEvent: {}
[10.122228] (-) TimerEvent: {}
[10.222446] (-) TimerEvent: {}
[10.322669] (-) TimerEvent: {}
[10.422901] (-) TimerEvent: {}
[10.523312] (-) TimerEvent: {}
[10.623547] (-) TimerEvent: {}
[10.723939] (-) TimerEvent: {}
[10.824186] (-) TimerEvent: {}
[10.924407] (-) TimerEvent: {}
[11.024619] (-) TimerEvent: {}
[11.124943] (-) TimerEvent: {}
[11.225293] (-) TimerEvent: {}
[11.325514] (-) TimerEvent: {}
[11.425771] (-) TimerEvent: {}
[11.526113] (-) TimerEvent: {}
[11.626328] (-) TimerEvent: {}
[11.726538] (-) TimerEvent: {}
[11.826891] (-) TimerEvent: {}
[11.927235] (-) TimerEvent: {}
[12.027473] (-) TimerEvent: {}
[12.127676] (-) TimerEvent: {}
[12.227988] (-) TimerEvent: {}
[12.328168] (-) TimerEvent: {}
[12.428377] (-) TimerEvent: {}
[12.528667] (-) TimerEvent: {}
[12.628970] (-) TimerEvent: {}
[12.729280] (-) TimerEvent: {}
[12.829521] (-) TimerEvent: {}
[12.929746] (-) TimerEvent: {}
[13.030115] (-) TimerEvent: {}
[13.130336] (-) TimerEvent: {}
[13.230563] (-) TimerEvent: {}
[13.330812] (-) TimerEvent: {}
[13.431046] (-) TimerEvent: {}
[13.531298] (-) TimerEvent: {}
[13.631513] (-) TimerEvent: {}
[13.731856] (-) TimerEvent: {}
[13.832085] (-) TimerEvent: {}
[13.932418] (-) TimerEvent: {}
[14.032706] (-) TimerEvent: {}
[14.132951] (-) TimerEvent: {}
[14.233285] (-) TimerEvent: {}
[14.333632] (-) TimerEvent: {}
[14.433979] (-) TimerEvent: {}
[14.534337] (-) TimerEvent: {}
[14.634549] (-) TimerEvent: {}
[14.734804] (-) TimerEvent: {}
[14.835148] (-) TimerEvent: {}
[14.935421] (-) TimerEvent: {}
[15.035679] (-) TimerEvent: {}
[15.136023] (-) TimerEvent: {}
[15.236299] (-) TimerEvent: {}
[15.336638] (-) TimerEvent: {}
[15.436932] (-) TimerEvent: {}
[15.537151] (-) TimerEvent: {}
[15.637327] (-) TimerEvent: {}
[15.737501] (-) TimerEvent: {}
[15.837705] (-) TimerEvent: {}
[15.937888] (-) TimerEvent: {}
[16.038222] (-) TimerEvent: {}
[16.138407] (-) TimerEvent: {}
[16.238619] (-) TimerEvent: {}
[16.338846] (-) TimerEvent: {}
[16.439018] (-) TimerEvent: {}
[16.539206] (-) TimerEvent: {}
[16.639376] (-) TimerEvent: {}
[16.739568] (-) TimerEvent: {}
[16.839765] (-) TimerEvent: {}
[16.939982] (-) TimerEvent: {}
[17.040196] (-) TimerEvent: {}
[17.140374] (-) TimerEvent: {}
[17.240563] (-) TimerEvent: {}
[17.340752] (-) TimerEvent: {}
[17.441035] (-) TimerEvent: {}
[17.541279] (-) TimerEvent: {}
[17.641458] (-) TimerEvent: {}
[17.741687] (-) TimerEvent: {}
[17.841995] (-) TimerEvent: {}
[17.942327] (-) TimerEvent: {}
[18.042507] (-) TimerEvent: {}
[18.142789] (-) TimerEvent: {}
[18.242968] (-) TimerEvent: {}
[18.343196] (-) TimerEvent: {}
[18.443389] (-) TimerEvent: {}
[18.543690] (-) TimerEvent: {}
[18.644040] (-) TimerEvent: {}
[18.744314] (-) TimerEvent: {}
[18.844519] (-) TimerEvent: {}
[18.944755] (-) TimerEvent: {}
[19.044997] (-) TimerEvent: {}
[19.145220] (-) TimerEvent: {}
[19.245443] (-) TimerEvent: {}
[19.345677] (-) TimerEvent: {}
[19.445932] (-) TimerEvent: {}
[19.546203] (-) TimerEvent: {}
[19.646391] (-) TimerEvent: {}
[19.746602] (-) TimerEvent: {}
[19.846788] (-) TimerEvent: {}
[19.947092] (-) TimerEvent: {}
[20.047315] (-) TimerEvent: {}
[20.147486] (-) TimerEvent: {}
[20.247720] (-) TimerEvent: {}
[20.348021] (-) TimerEvent: {}
[20.448203] (-) TimerEvent: {}
[20.548361] (-) TimerEvent: {}
[20.648547] (-) TimerEvent: {}
[20.748768] (-) TimerEvent: {}
[20.849060] (-) TimerEvent: {}
[20.949278] (-) TimerEvent: {}
[20.961341] (local_planner) StdoutLine: {'line': b'[100%] Linking CXX executable localPlanner\n'}
[21.049371] (-) TimerEvent: {}
[21.149568] (-) TimerEvent: {}
[21.249807] (-) TimerEvent: {}
[21.350045] (-) TimerEvent: {}
[21.450256] (-) TimerEvent: {}
[21.550449] (-) TimerEvent: {}
[21.650661] (-) TimerEvent: {}
[21.750871] (-) TimerEvent: {}
[21.851172] (-) TimerEvent: {}
[21.951377] (-) TimerEvent: {}
[22.051619] (-) TimerEvent: {}
[22.151856] (-) TimerEvent: {}
[22.252075] (-) TimerEvent: {}
[22.352283] (-) TimerEvent: {}
[22.452463] (-) TimerEvent: {}
[22.552662] (-) TimerEvent: {}
[22.652971] (-) TimerEvent: {}
[22.753324] (-) TimerEvent: {}
[22.853507] (-) TimerEvent: {}
[22.892235] (local_planner) StdoutLine: {'line': b'[100%] Built target localPlanner\n'}
[22.900009] (local_planner) CommandEnded: {'returncode': 0}
[22.900672] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'install'}
[22.911659] (local_planner) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('CLAUDE_CODE_ENTRYPOINT', 'cli'), ('LC_TIME', 'zh_CN.UTF-8'), ('GIT_EDITOR', 'true'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1721'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2255'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '55520'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('ANTHROPIC_BASE_URL', 'https://gaccodeapi.com'), ('TERM', 'xterm-256color'), ('OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE', 'delta'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('ANTHROPIC_API_KEY', 'sk-hHAkxaMgu7ISI42NI97HrlnSmZkSYwI4fkMk3xKdHp6QDldQ'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('COREPACK_ENABLE_AUTO_PIN', '0'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-1a39ff7581ade6bb.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dcb10a45d5.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:6ae87ae5-7baf-4b26-89ab-8d76be68d48f'), ('ANTHROPIC_AUTH_TOKEN', 'sk-hHAkxaMgu7ISI42NI97HrlnSmZkSYwI4fkMk3xKdHp6QDldQ'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('CLAUDECODE', '1'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[22.922816] (local_planner) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[22.923126] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/localPlanner\n'}
[22.938592] (local_planner) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/localPlanner" to ""\n'}
[22.938974] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/pathFollower\n'}
[22.939063] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch\n'}
[22.939146] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch/local_planner.launch.py\n'}
[22.939223] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch/local_planner.launch\n'}
[22.939275] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths\n'}
[22.939319] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/startPaths.ply\n'}
[22.939365] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/correspondences.txt\n'}
[22.939405] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/pathList.ply\n'}
[22.939443] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/paths.ply\n'}
[22.939480] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/path_generator.m\n'}
[22.939519] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/config\n'}
[22.939556] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/package_run_dependencies/local_planner\n'}
[22.939595] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/parent_prefix_path/local_planner\n'}
[22.939632] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/ament_prefix_path.sh\n'}
[22.939670] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/ament_prefix_path.dsv\n'}
[22.939715] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/path.sh\n'}
[22.939757] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/path.dsv\n'}
[22.939792] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.bash\n'}
[22.939829] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.sh\n'}
[22.939874] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.zsh\n'}
[22.939914] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.dsv\n'}
[22.939986] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv\n'}
[22.940026] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/packages/local_planner\n'}
[22.940142] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/cmake/local_plannerConfig.cmake\n'}
[22.940190] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/cmake/local_plannerConfig-version.cmake\n'}
[22.940232] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.xml\n'}
[22.940633] (local_planner) CommandEnded: {'returncode': 0}
[22.953637] (-) TimerEvent: {}
[22.954844] (local_planner) JobEnded: {'identifier': 'local_planner', 'rc': 0}
[22.955242] (-) EventReactorShutdown: {}
