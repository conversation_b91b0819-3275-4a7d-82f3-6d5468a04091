[0.096s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'local_planner']
[0.096s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['local_planner'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x71af43000f70>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x71af43000b20>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x71af43000b20>>)
[0.322s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.322s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.322s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.322s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.322s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.322s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.322s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/saoxueche0808pm/saoxueche'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['ignore', 'ignore_ament_install']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'ignore'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'ignore_ament_install'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['colcon_pkg']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'colcon_pkg'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['colcon_meta']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'colcon_meta'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['ros']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'ros'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['cmake', 'python']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'cmake'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'python'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['python_setup_py']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'python_setup_py'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['ignore', 'ignore_ament_install']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ignore'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ignore_ament_install'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['colcon_pkg']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'colcon_pkg'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['colcon_meta']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'colcon_meta'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['ros']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ros'
[0.347s] DEBUG:colcon.colcon_core.package_identification:Package 'src/integrated_navigation' with type 'ros.ament_cmake' and name 'integrated_navigation'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['ignore', 'ignore_ament_install']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ignore'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ignore_ament_install'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['colcon_pkg']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'colcon_pkg'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['colcon_meta']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'colcon_meta'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['ros']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ros'
[0.349s] DEBUG:colcon.colcon_core.package_identification:Package 'src/loam_interface' with type 'ros.ament_cmake' and name 'loam_interface'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ignore', 'ignore_ament_install']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore_ament_install'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_pkg']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_pkg'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_meta']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_meta'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ros']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ros'
[0.350s] DEBUG:colcon.colcon_core.package_identification:Package 'src/local_planner' with type 'ros.ament_cmake' and name 'local_planner'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['ignore', 'ignore_ament_install']
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ignore'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ignore_ament_install'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['colcon_pkg']
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'colcon_pkg'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['colcon_meta']
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'colcon_meta'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['ros']
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ros'
[0.351s] DEBUG:colcon.colcon_core.package_identification:Package 'src/nav2_smac_test' with type 'ros.ament_cmake' and name 'nav2_smac_test'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['ignore', 'ignore_ament_install']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ignore'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ignore_ament_install'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['colcon_pkg']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'colcon_pkg'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['colcon_meta']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'colcon_meta'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['ros']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ros'
[0.352s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sensor_scan_generation' with type 'ros.ament_cmake' and name 'sensor_scan_generation'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ignore', 'ignore_ament_install']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore_ament_install'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_pkg']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_pkg'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_meta']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_meta'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ros']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ros'
[0.353s] DEBUG:colcon.colcon_core.package_identification:Package 'src/terrain_analysis' with type 'ros.ament_cmake' and name 'terrain_analysis'
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['ignore', 'ignore_ament_install']
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ignore'
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ignore_ament_install'
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['colcon_pkg']
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'colcon_pkg'
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['colcon_meta']
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'colcon_meta'
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['ros']
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ros'
[0.354s] DEBUG:colcon.colcon_core.package_identification:Package 'src/terrain_analysis_ext' with type 'ros.ament_cmake' and name 'terrain_analysis_ext'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extension 'ignore'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) ignored
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'ignore'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) ignored
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['ignore', 'ignore_ament_install']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ignore'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ignore_ament_install'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['colcon_pkg']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'colcon_pkg'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['colcon_meta']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'colcon_meta'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['ros']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ros'
[0.356s] DEBUG:colcon.colcon_core.package_identification:Package 'src/visualization_tools' with type 'ros.ament_cmake' and name 'visualization_tools'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['ignore', 'ignore_ament_install']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ignore'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ignore_ament_install'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['colcon_pkg']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'colcon_pkg'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['colcon_meta']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'colcon_meta'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['ros']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ros'
[0.357s] DEBUG:colcon.colcon_core.package_identification:Package 'src/waypoint_example' with type 'ros.ament_cmake' and name 'waypoint_example'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['ignore', 'ignore_ament_install']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ignore'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ignore_ament_install'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['colcon_pkg']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'colcon_pkg'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['colcon_meta']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'colcon_meta'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['ros']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ros'
[0.358s] DEBUG:colcon.colcon_core.package_identification:Package 'src/waypoint_rviz_plugin' with type 'ros.ament_cmake' and name 'waypoint_rviz_plugin'
[0.358s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.358s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.358s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.358s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.358s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.391s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'integrated_navigation' in 'src/integrated_navigation'
[0.392s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'loam_interface' in 'src/loam_interface'
[0.392s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'nav2_smac_test' in 'src/nav2_smac_test'
[0.392s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'sensor_scan_generation' in 'src/sensor_scan_generation'
[0.392s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'terrain_analysis' in 'src/terrain_analysis'
[0.392s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'terrain_analysis_ext' in 'src/terrain_analysis_ext'
[0.392s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'visualization_tools' in 'src/visualization_tools'
[0.392s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'waypoint_example' in 'src/waypoint_example'
[0.392s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'waypoint_rviz_plugin' in 'src/waypoint_rviz_plugin'
[0.392s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.392s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.395s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 473 installed packages in /opt/ros/humble
[0.397s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.545s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_args' from command line to 'None'
[0.545s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target' from command line to 'None'
[0.545s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.545s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.545s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.545s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.545s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.545s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.545s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.545s] DEBUG:colcon.colcon_core.verb:Building package 'local_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner', 'symlink_install': False, 'test_result_base': None}
[0.545s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.547s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.547s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner' with build type 'ament_cmake'
[0.547s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner'
[0.551s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.551s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.551s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.560s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner -- -j16 -l16
[23.447s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner -- -j16 -l16
[23.460s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner
[23.487s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[23.488s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner
[23.491s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake module files
[23.491s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake config files
[23.492s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[23.492s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[23.492s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[23.493s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[23.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib'
[23.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[23.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/pkgconfig/local_planner.pc'
[23.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/python3.10/site-packages'
[23.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[23.495s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.ps1'
[23.495s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv'
[23.496s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.sh'
[23.496s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.bash'
[23.497s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.zsh'
[23.497s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/colcon-core/packages/local_planner)
[23.497s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[23.497s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake module files
[23.498s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake config files
[23.498s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[23.498s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[23.498s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[23.499s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[23.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib'
[23.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[23.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/pkgconfig/local_planner.pc'
[23.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/python3.10/site-packages'
[23.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[23.499s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.ps1'
[23.500s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv'
[23.500s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.sh'
[23.501s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.bash'
[23.501s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.zsh'
[23.501s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/colcon-core/packages/local_planner)
[23.501s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[23.502s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[23.502s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[23.502s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[23.505s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[23.505s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[23.506s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[23.519s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[23.520s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.ps1'
[23.521s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/saoxueche0808pm/saoxueche/install/_local_setup_util_ps1.py'
[23.522s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.ps1'
[23.523s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.sh'
[23.523s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/saoxueche0808pm/saoxueche/install/_local_setup_util_sh.py'
[23.524s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.sh'
[23.525s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.bash'
[23.525s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.bash'
[23.526s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.zsh'
[23.527s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.zsh'
