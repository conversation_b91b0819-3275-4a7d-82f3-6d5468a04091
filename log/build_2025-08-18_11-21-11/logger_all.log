[0.085s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.086s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x721994f54e80>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x721994f54a30>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x721994f54a30>>)
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.307s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/saoxueche0808pm/saoxueche'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.307s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['ignore', 'ignore_ament_install']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'ignore'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'ignore_ament_install'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['colcon_pkg']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'colcon_pkg'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['colcon_meta']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'colcon_meta'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['ros']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'ros'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['cmake', 'python']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'cmake'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'python'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['python_setup_py']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'python_setup_py'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['ignore', 'ignore_ament_install']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ignore'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ignore_ament_install'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['colcon_pkg']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'colcon_pkg'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['colcon_meta']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'colcon_meta'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['ros']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ros'
[0.320s] DEBUG:colcon.colcon_core.package_identification:Package 'src/integrated_navigation' with type 'ros.ament_cmake' and name 'integrated_navigation'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['ignore', 'ignore_ament_install']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ignore'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ignore_ament_install'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['colcon_pkg']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'colcon_pkg'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['colcon_meta']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'colcon_meta'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['ros']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ros'
[0.321s] DEBUG:colcon.colcon_core.package_identification:Package 'src/loam_interface' with type 'ros.ament_cmake' and name 'loam_interface'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ignore', 'ignore_ament_install']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore_ament_install'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_pkg']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_pkg'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_meta']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_meta'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ros']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ros'
[0.322s] DEBUG:colcon.colcon_core.package_identification:Package 'src/local_planner' with type 'ros.ament_cmake' and name 'local_planner'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac) by extensions ['ignore', 'ignore_ament_install']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac) by extension 'ignore'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac) by extension 'ignore_ament_install'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac) by extensions ['colcon_pkg']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac) by extension 'colcon_pkg'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac) by extensions ['colcon_meta']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac) by extension 'colcon_meta'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac) by extensions ['ros']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac) by extension 'ros'
[0.324s] DEBUG:colcon.colcon_core.package_identification:Package 'src/nav2_smac' with type 'ros.ament_cmake' and name 'nav2_smac'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['ignore', 'ignore_ament_install']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ignore'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ignore_ament_install'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['colcon_pkg']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'colcon_pkg'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['colcon_meta']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'colcon_meta'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['ros']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ros'
[0.325s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sensor_scan_generation' with type 'ros.ament_cmake' and name 'sensor_scan_generation'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ignore', 'ignore_ament_install']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore_ament_install'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_pkg']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_pkg'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_meta']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_meta'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ros']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ros'
[0.326s] DEBUG:colcon.colcon_core.package_identification:Package 'src/terrain_analysis' with type 'ros.ament_cmake' and name 'terrain_analysis'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['ignore', 'ignore_ament_install']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ignore'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ignore_ament_install'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['colcon_pkg']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'colcon_pkg'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['colcon_meta']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'colcon_meta'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['ros']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ros'
[0.327s] DEBUG:colcon.colcon_core.package_identification:Package 'src/terrain_analysis_ext' with type 'ros.ament_cmake' and name 'terrain_analysis_ext'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extension 'ignore'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) ignored
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'ignore'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) ignored
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['ignore', 'ignore_ament_install']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ignore'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ignore_ament_install'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['colcon_pkg']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'colcon_pkg'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['colcon_meta']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'colcon_meta'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['ros']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ros'
[0.328s] DEBUG:colcon.colcon_core.package_identification:Package 'src/visualization_tools' with type 'ros.ament_cmake' and name 'visualization_tools'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['ignore', 'ignore_ament_install']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ignore'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ignore_ament_install'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['colcon_pkg']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'colcon_pkg'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['colcon_meta']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'colcon_meta'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['ros']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ros'
[0.329s] DEBUG:colcon.colcon_core.package_identification:Package 'src/waypoint_example' with type 'ros.ament_cmake' and name 'waypoint_example'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['ignore', 'ignore_ament_install']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ignore'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ignore_ament_install'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['colcon_pkg']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'colcon_pkg'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['colcon_meta']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'colcon_meta'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['ros']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ros'
[0.330s] DEBUG:colcon.colcon_core.package_identification:Package 'src/waypoint_rviz_plugin' with type 'ros.ament_cmake' and name 'waypoint_rviz_plugin'
[0.330s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.330s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.330s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.330s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.330s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.351s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.352s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.353s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 10 installed packages in /home/<USER>/saoxueche0808pm/saoxueche/install
[0.355s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 473 installed packages in /opt/ros/humble
[0.356s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.388s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_args' from command line to 'None'
[0.388s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_target' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_clean_cache' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_clean_first' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'cmake_force_configure' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'ament_cmake_args' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'catkin_cmake_args' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'integrated_navigation' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.389s] DEBUG:colcon.colcon_core.verb:Building package 'integrated_navigation' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/integrated_navigation', 'symlink_install': False, 'test_result_base': None}
[0.389s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_args' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_target' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_clean_cache' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_clean_first' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'cmake_force_configure' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'ament_cmake_args' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'catkin_cmake_args' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'loam_interface' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.389s] DEBUG:colcon.colcon_core.verb:Building package 'loam_interface' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/loam_interface', 'symlink_install': False, 'test_result_base': None}
[0.389s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_args' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.389s] DEBUG:colcon.colcon_core.verb:Building package 'local_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner', 'symlink_install': False, 'test_result_base': None}
[0.389s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac' build argument 'cmake_args' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac' build argument 'cmake_target' from command line to 'None'
[0.389s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac' build argument 'cmake_clean_cache' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac' build argument 'cmake_clean_first' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac' build argument 'cmake_force_configure' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac' build argument 'ament_cmake_args' from command line to 'None'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac' build argument 'catkin_cmake_args' from command line to 'None'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.390s] DEBUG:colcon.colcon_core.verb:Building package 'nav2_smac' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/nav2_smac', 'symlink_install': False, 'test_result_base': None}
[0.390s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_args' from command line to 'None'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_target' from command line to 'None'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_clean_cache' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_clean_first' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'cmake_force_configure' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'ament_cmake_args' from command line to 'None'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'catkin_cmake_args' from command line to 'None'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'sensor_scan_generation' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.390s] DEBUG:colcon.colcon_core.verb:Building package 'sensor_scan_generation' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/sensor_scan_generation', 'symlink_install': False, 'test_result_base': None}
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_args' from command line to 'None'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_target' from command line to 'None'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_clean_cache' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_clean_first' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_force_configure' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'ament_cmake_args' from command line to 'None'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'catkin_cmake_args' from command line to 'None'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.390s] DEBUG:colcon.colcon_core.verb:Building package 'terrain_analysis' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis', 'symlink_install': False, 'test_result_base': None}
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_args' from command line to 'None'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_target' from command line to 'None'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_clean_cache' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_clean_first' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'cmake_force_configure' from command line to 'False'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'ament_cmake_args' from command line to 'None'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'catkin_cmake_args' from command line to 'None'
[0.390s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis_ext' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.390s] DEBUG:colcon.colcon_core.verb:Building package 'terrain_analysis_ext' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis_ext', 'symlink_install': False, 'test_result_base': None}
[0.391s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_args' from command line to 'None'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_target' from command line to 'None'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_clean_cache' from command line to 'False'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_clean_first' from command line to 'False'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'cmake_force_configure' from command line to 'False'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'ament_cmake_args' from command line to 'None'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'catkin_cmake_args' from command line to 'None'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'visualization_tools' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.391s] DEBUG:colcon.colcon_core.verb:Building package 'visualization_tools' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/visualization_tools', 'symlink_install': False, 'test_result_base': None}
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_args' from command line to 'None'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_target' from command line to 'None'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_clean_cache' from command line to 'False'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_clean_first' from command line to 'False'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'cmake_force_configure' from command line to 'False'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'ament_cmake_args' from command line to 'None'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'catkin_cmake_args' from command line to 'None'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_example' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.391s] DEBUG:colcon.colcon_core.verb:Building package 'waypoint_example' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_example', 'symlink_install': False, 'test_result_base': None}
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_args' from command line to 'None'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_target' from command line to 'None'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_clean_cache' from command line to 'False'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_clean_first' from command line to 'False'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'cmake_force_configure' from command line to 'False'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'ament_cmake_args' from command line to 'None'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'catkin_cmake_args' from command line to 'None'
[0.391s] Level 5:colcon.colcon_core.verb:set package 'waypoint_rviz_plugin' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.391s] DEBUG:colcon.colcon_core.verb:Building package 'waypoint_rviz_plugin' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_rviz_plugin', 'symlink_install': False, 'test_result_base': None}
[0.391s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.392s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.392s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/integrated_navigation' with build type 'ament_cmake'
[0.392s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/integrated_navigation'
[0.395s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.395s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.395s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.398s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/loam_interface' with build type 'ament_cmake'
[0.398s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/loam_interface'
[0.398s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.398s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.400s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner' with build type 'ament_cmake'
[0.401s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner'
[0.401s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.401s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.403s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/nav2_smac' with build type 'ament_cmake'
[0.403s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/nav2_smac'
[0.403s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.403s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.406s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/sensor_scan_generation' with build type 'ament_cmake'
[0.406s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/sensor_scan_generation'
[0.406s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.406s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.408s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis' with build type 'ament_cmake'
[0.408s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis'
[0.409s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.409s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.411s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis_ext' with build type 'ament_cmake'
[0.411s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis_ext'
[0.411s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.411s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.414s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/visualization_tools' with build type 'ament_cmake'
[0.414s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/visualization_tools'
[0.414s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.414s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.416s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_example' with build type 'ament_cmake'
[0.416s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_example'
[0.416s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.416s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.419s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_rviz_plugin' with build type 'ament_cmake'
[0.419s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_rviz_plugin'
[0.419s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.419s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.426s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation -- -j16 -l16
[0.428s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface -- -j16 -l16
[0.430s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner -- -j16 -l16
[0.432s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac -- -j16 -l16
[0.434s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation -- -j16 -l16
[0.436s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis -- -j16 -l16
[0.439s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext -- -j16 -l16
[0.441s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools -- -j16 -l16
[0.444s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example -- -j16 -l16
[0.450s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin -- -j16 -l16
[0.450s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation -- -j16 -l16
[0.458s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation
[0.461s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac -- -j16 -l16
[0.462s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac
[0.465s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(integrated_navigation)
[0.466s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation
[0.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation' for CMake module files
[0.472s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation' for CMake config files
[0.472s] Level 1:colcon.colcon_core.shell:create_environment_hook('integrated_navigation', 'cmake_prefix_path')
[0.472s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.ps1'
[0.473s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.dsv'
[0.473s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.sh'
[0.474s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/bin'
[0.474s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/lib/pkgconfig/integrated_navigation.pc'
[0.474s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/lib/python3.10/site-packages'
[0.474s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/bin'
[0.475s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.ps1'
[0.476s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.dsv'
[0.477s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.sh'
[0.478s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.bash'
[0.479s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.zsh'
[0.480s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/colcon-core/packages/integrated_navigation)
[0.481s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(integrated_navigation)
[0.481s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation' for CMake module files
[0.482s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation' for CMake config files
[0.482s] Level 1:colcon.colcon_core.shell:create_environment_hook('integrated_navigation', 'cmake_prefix_path')
[0.482s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.ps1'
[0.482s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.dsv'
[0.482s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/hook/cmake_prefix_path.sh'
[0.483s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/bin'
[0.483s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/lib/pkgconfig/integrated_navigation.pc'
[0.483s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/lib/python3.10/site-packages'
[0.483s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/bin'
[0.483s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.ps1'
[0.484s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.dsv'
[0.484s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.sh'
[0.484s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.bash'
[0.484s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/integrated_navigation/package.zsh'
[0.485s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/integrated_navigation/share/colcon-core/packages/integrated_navigation)
[0.486s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(nav2_smac)
[0.486s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac' for CMake module files
[0.486s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac
[0.487s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac' for CMake config files
[0.487s] Level 1:colcon.colcon_core.shell:create_environment_hook('nav2_smac', 'cmake_prefix_path')
[0.487s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/hook/cmake_prefix_path.ps1'
[0.488s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/hook/cmake_prefix_path.dsv'
[0.488s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/hook/cmake_prefix_path.sh'
[0.489s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/bin'
[0.489s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/lib/pkgconfig/nav2_smac.pc'
[0.489s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/lib/python3.10/site-packages'
[0.489s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/bin'
[0.490s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/package.ps1'
[0.491s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/package.dsv'
[0.491s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/package.sh'
[0.491s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/package.bash'
[0.491s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/package.zsh'
[0.492s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/colcon-core/packages/nav2_smac)
[0.492s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(nav2_smac)
[0.492s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac' for CMake module files
[0.493s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac' for CMake config files
[0.493s] Level 1:colcon.colcon_core.shell:create_environment_hook('nav2_smac', 'cmake_prefix_path')
[0.493s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/hook/cmake_prefix_path.ps1'
[0.494s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/hook/cmake_prefix_path.dsv'
[0.494s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/hook/cmake_prefix_path.sh'
[0.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/bin'
[0.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/lib/pkgconfig/nav2_smac.pc'
[0.495s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/lib/python3.10/site-packages'
[0.495s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/bin'
[0.495s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/package.ps1'
[0.496s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/package.dsv'
[0.496s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/package.sh'
[0.496s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/package.bash'
[0.497s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/nav2_smac/package.zsh'
[0.497s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac/share/colcon-core/packages/nav2_smac)
[0.499s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner -- -j16 -l16
[0.500s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner
[0.501s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis -- -j16 -l16
[0.503s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis
[0.504s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface -- -j16 -l16
[0.507s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface
[0.508s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation -- -j16 -l16
[0.511s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation
[0.514s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext -- -j16 -l16
[0.517s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext
[0.517s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example -- -j16 -l16
[0.518s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example
[0.519s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[0.519s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake module files
[0.520s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner
[0.520s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake config files
[0.520s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[0.521s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[0.521s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[0.521s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[0.522s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib'
[0.522s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[0.522s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/pkgconfig/local_planner.pc'
[0.522s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/python3.10/site-packages'
[0.522s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[0.522s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.ps1'
[0.523s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv'
[0.523s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.sh'
[0.523s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.bash'
[0.523s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.zsh'
[0.523s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/colcon-core/packages/local_planner)
[0.524s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[0.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake module files
[0.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake config files
[0.524s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[0.524s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[0.525s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[0.525s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[0.525s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib'
[0.525s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[0.525s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/pkgconfig/local_planner.pc'
[0.525s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/python3.10/site-packages'
[0.525s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[0.526s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.ps1'
[0.526s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv'
[0.526s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.sh'
[0.526s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.bash'
[0.526s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.zsh'
[0.527s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/colcon-core/packages/local_planner)
[0.527s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis)
[0.527s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis' for CMake module files
[0.528s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis' for CMake config files
[0.528s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis
[0.528s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis', 'cmake_prefix_path')
[0.528s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.ps1'
[0.528s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.dsv'
[0.528s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.sh'
[0.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib'
[0.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/bin'
[0.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib/pkgconfig/terrain_analysis.pc'
[0.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib/python3.10/site-packages'
[0.529s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/bin'
[0.529s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.ps1'
[0.529s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.dsv'
[0.530s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.sh'
[0.530s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.bash'
[0.530s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.zsh'
[0.530s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/colcon-core/packages/terrain_analysis)
[0.530s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis)
[0.531s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis' for CMake module files
[0.531s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis' for CMake config files
[0.531s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis', 'cmake_prefix_path')
[0.531s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.ps1'
[0.531s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.dsv'
[0.531s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.sh'
[0.532s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib'
[0.532s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/bin'
[0.532s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib/pkgconfig/terrain_analysis.pc'
[0.532s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/lib/python3.10/site-packages'
[0.532s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/bin'
[0.532s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.ps1'
[0.532s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.dsv'
[0.533s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.sh'
[0.533s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.bash'
[0.533s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/terrain_analysis/package.zsh'
[0.533s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis/share/colcon-core/packages/terrain_analysis)
[0.536s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(loam_interface)
[0.536s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface' for CMake module files
[0.537s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface
[0.537s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface' for CMake config files
[0.537s] Level 1:colcon.colcon_core.shell:create_environment_hook('loam_interface', 'cmake_prefix_path')
[0.537s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.ps1'
[0.538s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.dsv'
[0.538s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.sh'
[0.538s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib'
[0.538s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/bin'
[0.538s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib/pkgconfig/loam_interface.pc'
[0.539s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib/python3.10/site-packages'
[0.539s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/bin'
[0.539s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.ps1'
[0.539s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.dsv'
[0.539s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.sh'
[0.540s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.bash'
[0.540s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.zsh'
[0.540s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/colcon-core/packages/loam_interface)
[0.540s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(loam_interface)
[0.540s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface' for CMake module files
[0.541s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface' for CMake config files
[0.541s] Level 1:colcon.colcon_core.shell:create_environment_hook('loam_interface', 'cmake_prefix_path')
[0.541s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.ps1'
[0.541s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.dsv'
[0.541s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/hook/cmake_prefix_path.sh'
[0.542s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib'
[0.542s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/bin'
[0.542s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib/pkgconfig/loam_interface.pc'
[0.542s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/lib/python3.10/site-packages'
[0.542s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/bin'
[0.542s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.ps1'
[0.542s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.dsv'
[0.543s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.sh'
[0.543s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.bash'
[0.543s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/loam_interface/package.zsh'
[0.543s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/loam_interface/share/colcon-core/packages/loam_interface)
[0.543s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sensor_scan_generation)
[0.544s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation' for CMake module files
[0.544s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/sensor_scan_generation
[0.544s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation' for CMake config files
[0.544s] Level 1:colcon.colcon_core.shell:create_environment_hook('sensor_scan_generation', 'cmake_prefix_path')
[0.545s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.ps1'
[0.545s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.dsv'
[0.545s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.sh'
[0.545s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib'
[0.545s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/bin'
[0.545s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib/pkgconfig/sensor_scan_generation.pc'
[0.546s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib/python3.10/site-packages'
[0.546s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/bin'
[0.546s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.ps1'
[0.546s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv'
[0.546s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.sh'
[0.547s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.bash'
[0.547s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.zsh'
[0.547s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/colcon-core/packages/sensor_scan_generation)
[0.547s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(sensor_scan_generation)
[0.547s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation' for CMake module files
[0.548s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation' for CMake config files
[0.548s] Level 1:colcon.colcon_core.shell:create_environment_hook('sensor_scan_generation', 'cmake_prefix_path')
[0.548s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.ps1'
[0.548s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.dsv'
[0.548s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/hook/cmake_prefix_path.sh'
[0.549s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib'
[0.549s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/bin'
[0.549s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib/pkgconfig/sensor_scan_generation.pc'
[0.549s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/lib/python3.10/site-packages'
[0.549s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/bin'
[0.549s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.ps1'
[0.549s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.dsv'
[0.550s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.sh'
[0.550s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.bash'
[0.550s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/sensor_scan_generation/package.zsh'
[0.551s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/sensor_scan_generation/share/colcon-core/packages/sensor_scan_generation)
[0.551s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis_ext)
[0.551s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext' for CMake module files
[0.551s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext' for CMake config files
[0.552s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext
[0.552s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis_ext', 'cmake_prefix_path')
[0.552s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.ps1'
[0.552s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.dsv'
[0.552s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.sh'
[0.553s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib'
[0.553s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/bin'
[0.553s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib/pkgconfig/terrain_analysis_ext.pc'
[0.553s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib/python3.10/site-packages'
[0.553s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/bin'
[0.553s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.ps1'
[0.553s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.dsv'
[0.554s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.sh'
[0.554s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.bash'
[0.554s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.zsh'
[0.554s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/colcon-core/packages/terrain_analysis_ext)
[0.554s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis_ext)
[0.554s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext' for CMake module files
[0.555s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext' for CMake config files
[0.555s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis_ext', 'cmake_prefix_path')
[0.555s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.ps1'
[0.555s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.dsv'
[0.555s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/hook/cmake_prefix_path.sh'
[0.556s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib'
[0.556s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/bin'
[0.556s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib/pkgconfig/terrain_analysis_ext.pc'
[0.556s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/lib/python3.10/site-packages'
[0.556s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/bin'
[0.556s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.ps1'
[0.556s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.dsv'
[0.557s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.sh'
[0.557s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.bash'
[0.557s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/terrain_analysis_ext/package.zsh'
[0.557s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/terrain_analysis_ext/share/colcon-core/packages/terrain_analysis_ext)
[0.557s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_example)
[0.557s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example' for CMake module files
[0.558s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example
[0.558s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example' for CMake config files
[0.558s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_example', 'cmake_prefix_path')
[0.558s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.ps1'
[0.558s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.dsv'
[0.559s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.sh'
[0.559s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib'
[0.559s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/bin'
[0.559s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib/pkgconfig/waypoint_example.pc'
[0.559s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib/python3.10/site-packages'
[0.559s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/bin'
[0.559s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.ps1'
[0.560s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.dsv'
[0.560s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.sh'
[0.560s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.bash'
[0.560s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.zsh'
[0.560s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/colcon-core/packages/waypoint_example)
[0.561s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_example)
[0.561s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example' for CMake module files
[0.561s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example' for CMake config files
[0.561s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_example', 'cmake_prefix_path')
[0.561s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.ps1'
[0.561s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.dsv'
[0.562s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/hook/cmake_prefix_path.sh'
[0.562s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib'
[0.562s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/bin'
[0.562s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib/pkgconfig/waypoint_example.pc'
[0.562s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/lib/python3.10/site-packages'
[0.562s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/bin'
[0.562s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.ps1'
[0.563s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.dsv'
[0.563s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.sh'
[0.563s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.bash'
[0.563s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/waypoint_example/package.zsh'
[0.563s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_example/share/colcon-core/packages/waypoint_example)
[0.564s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin -- -j16 -l16
[0.565s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin
[0.574s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_rviz_plugin)
[0.574s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin
[0.574s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin' for CMake module files
[0.574s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin' for CMake config files
[0.575s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_rviz_plugin', 'cmake_prefix_path')
[0.575s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.ps1'
[0.575s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.dsv'
[0.576s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.sh'
[0.576s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib'
[0.576s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/bin'
[0.576s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib/pkgconfig/waypoint_rviz_plugin.pc'
[0.577s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib/python3.10/site-packages'
[0.577s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/bin'
[0.577s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.ps1'
[0.577s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.dsv'
[0.578s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.sh'
[0.578s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.bash'
[0.578s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.zsh'
[0.578s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/colcon-core/packages/waypoint_rviz_plugin)
[0.578s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(waypoint_rviz_plugin)
[0.579s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin' for CMake module files
[0.579s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin' for CMake config files
[0.579s] Level 1:colcon.colcon_core.shell:create_environment_hook('waypoint_rviz_plugin', 'cmake_prefix_path')
[0.579s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.ps1'
[0.579s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.dsv'
[0.579s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/hook/cmake_prefix_path.sh'
[0.580s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib'
[0.580s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/bin'
[0.580s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib/pkgconfig/waypoint_rviz_plugin.pc'
[0.580s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/lib/python3.10/site-packages'
[0.580s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/bin'
[0.580s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.ps1'
[0.581s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.dsv'
[0.581s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.sh'
[0.581s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.bash'
[0.581s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/waypoint_rviz_plugin/package.zsh'
[0.581s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin/share/colcon-core/packages/waypoint_rviz_plugin)
[0.677s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools -- -j16 -l16
[0.678s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools
[0.713s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(visualization_tools)
[0.713s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools' for CMake module files
[0.713s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools
[0.714s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools' for CMake config files
[0.714s] Level 1:colcon.colcon_core.shell:create_environment_hook('visualization_tools', 'cmake_prefix_path')
[0.714s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.ps1'
[0.715s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.dsv'
[0.715s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.sh'
[0.716s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib'
[0.716s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/bin'
[0.716s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/pkgconfig/visualization_tools.pc'
[0.716s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/python3.10/site-packages'
[0.716s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/bin'
[0.716s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.ps1'
[0.717s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.dsv'
[0.717s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.sh'
[0.717s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.bash'
[0.717s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.zsh'
[0.717s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/colcon-core/packages/visualization_tools)
[0.718s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(visualization_tools)
[0.718s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools' for CMake module files
[0.718s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools' for CMake config files
[0.718s] Level 1:colcon.colcon_core.shell:create_environment_hook('visualization_tools', 'cmake_prefix_path')
[0.718s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.ps1'
[0.719s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.dsv'
[0.719s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/hook/cmake_prefix_path.sh'
[0.719s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib'
[0.719s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/bin'
[0.719s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/pkgconfig/visualization_tools.pc'
[0.719s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/lib/python3.10/site-packages'
[0.719s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/bin'
[0.720s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.ps1'
[0.720s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.dsv'
[0.720s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.sh'
[0.720s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.bash'
[0.721s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/visualization_tools/package.zsh'
[0.721s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/visualization_tools/share/colcon-core/packages/visualization_tools)
[0.721s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.721s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.721s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.721s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.726s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.726s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.726s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.734s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.734s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.ps1'
[0.735s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/saoxueche0808pm/saoxueche/install/_local_setup_util_ps1.py'
[0.736s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.ps1'
[0.737s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.sh'
[0.737s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/saoxueche0808pm/saoxueche/install/_local_setup_util_sh.py'
[0.738s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.sh'
[0.739s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.bash'
[0.739s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.bash'
[0.740s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.zsh'
[0.740s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.zsh'
