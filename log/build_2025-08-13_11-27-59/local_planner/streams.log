[0.009s] Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner -- -j16 -l16
[0.079s] [ 25%] [32mBuilding CXX object CMakeFiles/localPlanner.dir/src/localPlanner.cpp.o[0m
[0.082s] [ 50%] [32mBuilding CXX object CMakeFiles/pathFollower.dir/src/pathFollower.cpp.o[0m
[4.143s] In file included from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24[m[K,
[4.143s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40[m[K,
[4.144s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24[m[K,
[4.144s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20[m[K,
[4.144s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25[m[K,
[4.144s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18[m[K,
[4.144s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20[m[K,
[4.144s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37[m[K,
[4.144s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25[m[K,
[4.144s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21[m[K,
[4.144s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
[4.144s]                  from [01m[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:7[m[K:
[4.144s] [01m[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:[m[K In function ‘[01m[Kvoid pathHandler(nav_msgs::msg::Path_<std::allocator<void> >::ConstSharedPtr)[m[K’:
[4.144s] [01m[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:140:33:[m[K [01;35m[Kwarning: [m[Kformat ‘[01m[K%ld[m[K’ expects argument of type ‘[01m[Klong int[m[K’, but argument 5 has type ‘[01m[Kint[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wformat=-Wformat=]8;;[m[K]
[4.145s]   140 |   RCLCPP_INFO(nh->get_logger(), [01;35m[K"Timestamp: %ld.%09ld"[m[K,
[4.145s]       |                                 [01;35m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[4.145s]   141 |               [32m[KpathIn->header.stamp.sec[m[K, pathIn->header.stamp.nanosec);
[4.145s]       |               [32m[K~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[4.145s]       |                                    [32m[K|[m[K
[4.145s]       |                                    [32m[Kint[m[K
[4.145s] [01m[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:140:47:[m[K [01;36m[Knote: [m[Kformat string is defined here
[4.145s]   140 |   RCLCPP_INFO(nh->get_logger(), "Timestamp: [01;36m[K%ld[m[K.%09ld",
[4.145s]       |                                             [01;36m[K~~^[m[K
[4.145s]       |                                               [01;36m[K|[m[K
[4.145s]       |                                               [01;36m[Klong int[m[K
[4.145s]       |                                             [32m[K%d[m[K
[4.145s] In file included from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24[m[K,
[4.145s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40[m[K,
[4.145s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24[m[K,
[4.145s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20[m[K,
[4.145s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25[m[K,
[4.145s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18[m[K,
[4.145s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20[m[K,
[4.145s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37[m[K,
[4.145s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25[m[K,
[4.145s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21[m[K,
[4.145s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
[4.145s]                  from [01m[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:7[m[K:
[4.145s] [01m[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:140:33:[m[K [01;35m[Kwarning: [m[Kformat ‘[01m[K%ld[m[K’ expects argument of type ‘[01m[Klong int[m[K’, but argument 6 has type ‘[01m[Kunsigned int[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wformat=-Wformat=]8;;[m[K]
[4.145s]   140 |   RCLCPP_INFO(nh->get_logger(), [01;35m[K"Timestamp: %ld.%09ld"[m[K,
[4.145s]       |                                 [01;35m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[4.145s]   141 |               pathIn->header.stamp.sec, [32m[KpathIn->header.stamp.nanosec[m[K);
[4.145s]       |                                         [32m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[4.145s]       |                                                              [32m[K|[m[K
[4.145s]       |                                                              [32m[Kunsigned int[m[K
[4.145s] [01m[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:140:53:[m[K [01;36m[Knote: [m[Kformat string is defined here
[4.146s]   140 |   RCLCPP_INFO(nh->get_logger(), "Timestamp: %ld.[01;36m[K%09ld[m[K",
[4.146s]       |                                                 [01;36m[K~~~~^[m[K
[4.146s]       |                                                     [01;36m[K|[m[K
[4.146s]       |                                                     [01;36m[Klong int[m[K
[4.146s]       |                                                 [32m[K%09d[m[K
[16.762s] [ 75%] [32m[1mLinking CXX executable pathFollower[0m
[18.142s] [ 75%] Built target pathFollower
[20.876s] [100%] [32m[1mLinking CXX executable localPlanner[0m
[22.817s] [100%] Built target localPlanner
[22.836s] Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner -- -j16 -l16
[22.852s] Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner
[22.866s] -- Install configuration: ""
[22.867s] -- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/localPlanner
[22.881s] -- Set runtime path of "/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/localPlanner" to ""
[22.882s] -- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/pathFollower
[22.890s] -- Set runtime path of "/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/pathFollower" to ""
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch/local_planner.launch.py
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch/local_planner.launch
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/startPaths.ply
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/correspondences.txt
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/pathList.ply
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/paths.ply
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/path_generator.m
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/config
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/package_run_dependencies/local_planner
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/parent_prefix_path/local_planner
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/ament_prefix_path.sh
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/ament_prefix_path.dsv
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/path.sh
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/path.dsv
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.bash
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.sh
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.zsh
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.dsv
[22.890s] -- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv
[22.890s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/packages/local_planner
[22.891s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/cmake/local_plannerConfig.cmake
[22.891s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/cmake/local_plannerConfig-version.cmake
[22.891s] -- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.xml
[22.892s] Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner
