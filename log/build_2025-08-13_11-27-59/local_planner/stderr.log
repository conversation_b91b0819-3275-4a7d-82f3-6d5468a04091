In file included from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
                 from [01m[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:7[m[K:
[01m[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:[m[K In function ‘[01m[Kvoid pathHandler(nav_msgs::msg::Path_<std::allocator<void> >::ConstSharedPtr)[m[K’:
[01m[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:140:33:[m[K [01;35m[Kwarning: [m[Kformat ‘[01m[K%ld[m[K’ expects argument of type ‘[01m[Klong int[m[K’, but argument 5 has type ‘[01m[Kint[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wformat=-Wformat=]8;;[m[K]
  140 |   RCLCPP_INFO(nh->get_logger(), [01;35m[K"Timestamp: %ld.%09ld"[m[K,
      |                                 [01;35m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
  141 |               [32m[KpathIn->header.stamp.sec[m[K, pathIn->header.stamp.nanosec);
      |               [32m[K~~~~~~~~~~~~~~~~~~~~~~~~[m[K
      |                                    [32m[K|[m[K
      |                                    [32m[Kint[m[K
[01m[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:140:47:[m[K [01;36m[Knote: [m[Kformat string is defined here
  140 |   RCLCPP_INFO(nh->get_logger(), "Timestamp: [01;36m[K%ld[m[K.%09ld",
      |                                             [01;36m[K~~^[m[K
      |                                               [01;36m[K|[m[K
      |                                               [01;36m[Klong int[m[K
      |                                             [32m[K%d[m[K
In file included from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
                 from [01m[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:7[m[K:
[01m[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:140:33:[m[K [01;35m[Kwarning: [m[Kformat ‘[01m[K%ld[m[K’ expects argument of type ‘[01m[Klong int[m[K’, but argument 6 has type ‘[01m[Kunsigned int[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wformat=-Wformat=]8;;[m[K]
  140 |   RCLCPP_INFO(nh->get_logger(), [01;35m[K"Timestamp: %ld.%09ld"[m[K,
      |                                 [01;35m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
  141 |               pathIn->header.stamp.sec, [32m[KpathIn->header.stamp.nanosec[m[K);
      |                                         [32m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
      |                                                              [32m[K|[m[K
      |                                                              [32m[Kunsigned int[m[K
[01m[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:140:53:[m[K [01;36m[Knote: [m[Kformat string is defined here
  140 |   RCLCPP_INFO(nh->get_logger(), "Timestamp: %ld.[01;36m[K%09ld[m[K",
      |                                                 [01;36m[K~~~~^[m[K
      |                                                     [01;36m[K|[m[K
      |                                                     [01;36m[Klong int[m[K
      |                                                 [32m[K%09d[m[K
