[0.000000] (-) TimerEvent: {}
[0.000330] (-) JobUnselected: {'identifier': 'integrated_navigation'}
[0.000348] (-) JobUnselected: {'identifier': 'loam_interface'}
[0.000359] (-) JobUnselected: {'identifier': 'nav2_smac_test'}
[0.000368] (-) JobUnselected: {'identifier': 'sensor_scan_generation'}
[0.000376] (-) JobUnselected: {'identifier': 'terrain_analysis'}
[0.000384] (-) JobUnselected: {'identifier': 'terrain_analysis_ext'}
[0.000392] (-) JobUnselected: {'identifier': 'visualization_tools'}
[0.000400] (-) JobUnselected: {'identifier': 'waypoint_example'}
[0.000408] (-) JobUnselected: {'identifier': 'waypoint_rviz_plugin'}
[0.000417] (local_planner) JobQueued: {'identifier': 'local_planner', 'dependencies': OrderedDict()}
[0.000429] (local_planner) JobStarted: {'identifier': 'local_planner'}
[0.005962] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'cmake'}
[0.007107] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'build'}
[0.007708] (local_planner) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/saoxueche0808pm/saoxueche'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1721'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2255'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '55520'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-1a39ff7581ade6bb.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dcb10a45d5.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:6ae87ae5-7baf-4b26-89ab-8d76be68d48f'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.079316] (local_planner) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/localPlanner.dir/src/localPlanner.cpp.o\x1b[0m\n'}
[0.082051] (local_planner) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/pathFollower.dir/src/pathFollower.cpp.o\x1b[0m\n'}
[0.099733] (-) TimerEvent: {}
[0.200267] (-) TimerEvent: {}
[0.300574] (-) TimerEvent: {}
[0.400848] (-) TimerEvent: {}
[0.501112] (-) TimerEvent: {}
[0.601414] (-) TimerEvent: {}
[0.701700] (-) TimerEvent: {}
[0.802008] (-) TimerEvent: {}
[0.902319] (-) TimerEvent: {}
[1.002649] (-) TimerEvent: {}
[1.102973] (-) TimerEvent: {}
[1.203292] (-) TimerEvent: {}
[1.303615] (-) TimerEvent: {}
[1.403938] (-) TimerEvent: {}
[1.504394] (-) TimerEvent: {}
[1.604697] (-) TimerEvent: {}
[1.705045] (-) TimerEvent: {}
[1.805400] (-) TimerEvent: {}
[1.905815] (-) TimerEvent: {}
[2.006215] (-) TimerEvent: {}
[2.106586] (-) TimerEvent: {}
[2.207024] (-) TimerEvent: {}
[2.307428] (-) TimerEvent: {}
[2.407803] (-) TimerEvent: {}
[2.508213] (-) TimerEvent: {}
[2.608700] (-) TimerEvent: {}
[2.709088] (-) TimerEvent: {}
[2.809451] (-) TimerEvent: {}
[2.909821] (-) TimerEvent: {}
[3.010213] (-) TimerEvent: {}
[3.110580] (-) TimerEvent: {}
[3.210925] (-) TimerEvent: {}
[3.311256] (-) TimerEvent: {}
[3.411623] (-) TimerEvent: {}
[3.511961] (-) TimerEvent: {}
[3.612454] (-) TimerEvent: {}
[3.712779] (-) TimerEvent: {}
[3.813108] (-) TimerEvent: {}
[3.913429] (-) TimerEvent: {}
[4.013728] (-) TimerEvent: {}
[4.114047] (-) TimerEvent: {}
[4.143387] (local_planner) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24\x1b[m\x1b[K,\n'}
[4.143789] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40\x1b[m\x1b[K,\n'}
[4.143892] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24\x1b[m\x1b[K,\n'}
[4.143983] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20\x1b[m\x1b[K,\n'}
[4.144068] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25\x1b[m\x1b[K,\n'}
[4.144151] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18\x1b[m\x1b[K,\n'}
[4.144235] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20\x1b[m\x1b[K,\n'}
[4.144315] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37\x1b[m\x1b[K,\n'}
[4.144393] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25\x1b[m\x1b[K,\n'}
[4.144473] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21\x1b[m\x1b[K,\n'}
[4.144552] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155\x1b[m\x1b[K,\n'}
[4.144636] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:7\x1b[m\x1b[K:\n'}
[4.144742] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid pathHandler(nav_msgs::msg::Path_<std::allocator<void> >::ConstSharedPtr)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[4.144885] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:140:33:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kformat \xe2\x80\x98\x1b[01m\x1b[K%ld\x1b[m\x1b[K\xe2\x80\x99 expects argument of type \xe2\x80\x98\x1b[01m\x1b[Klong int\x1b[m\x1b[K\xe2\x80\x99, but argument 5 has type \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wformat=\x07-Wformat=\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.144930] (local_planner) StderrLine: {'line': b'  140 |   RCLCPP_INFO(nh->get_logger(), \x1b[01;35m\x1b[K"Timestamp: %ld.%09ld"\x1b[m\x1b[K,\n'}
[4.144965] (local_planner) StderrLine: {'line': b'      |                                 \x1b[01;35m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[4.144998] (local_planner) StderrLine: {'line': b'  141 |               \x1b[32m\x1b[KpathIn->header.stamp.sec\x1b[m\x1b[K, pathIn->header.stamp.nanosec);\n'}
[4.145030] (local_planner) StderrLine: {'line': b'      |               \x1b[32m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[4.145062] (local_planner) StderrLine: {'line': b'      |                                    \x1b[32m\x1b[K|\x1b[m\x1b[K\n'}
[4.145092] (local_planner) StderrLine: {'line': b'      |                                    \x1b[32m\x1b[Kint\x1b[m\x1b[K\n'}
[4.145121] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:140:47:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kformat string is defined here\n'}
[4.145152] (local_planner) StderrLine: {'line': b'  140 |   RCLCPP_INFO(nh->get_logger(), "Timestamp: \x1b[01;36m\x1b[K%ld\x1b[m\x1b[K.%09ld",\n'}
[4.145192] (local_planner) StderrLine: {'line': b'      |                                             \x1b[01;36m\x1b[K~~^\x1b[m\x1b[K\n'}
[4.145222] (local_planner) StderrLine: {'line': b'      |                                               \x1b[01;36m\x1b[K|\x1b[m\x1b[K\n'}
[4.145250] (local_planner) StderrLine: {'line': b'      |                                               \x1b[01;36m\x1b[Klong int\x1b[m\x1b[K\n'}
[4.145280] (local_planner) StderrLine: {'line': b'      |                                             \x1b[32m\x1b[K%d\x1b[m\x1b[K\n'}
[4.145309] (local_planner) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24\x1b[m\x1b[K,\n'}
[4.145338] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40\x1b[m\x1b[K,\n'}
[4.145367] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24\x1b[m\x1b[K,\n'}
[4.145394] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20\x1b[m\x1b[K,\n'}
[4.145423] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25\x1b[m\x1b[K,\n'}
[4.145451] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18\x1b[m\x1b[K,\n'}
[4.145480] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20\x1b[m\x1b[K,\n'}
[4.145509] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37\x1b[m\x1b[K,\n'}
[4.145545] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25\x1b[m\x1b[K,\n'}
[4.145580] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21\x1b[m\x1b[K,\n'}
[4.145620] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155\x1b[m\x1b[K,\n'}
[4.145651] (local_planner) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:7\x1b[m\x1b[K:\n'}
[4.145682] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:140:33:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kformat \xe2\x80\x98\x1b[01m\x1b[K%ld\x1b[m\x1b[K\xe2\x80\x99 expects argument of type \xe2\x80\x98\x1b[01m\x1b[Klong int\x1b[m\x1b[K\xe2\x80\x99, but argument 6 has type \xe2\x80\x98\x1b[01m\x1b[Kunsigned int\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wformat=\x07-Wformat=\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.145722] (local_planner) StderrLine: {'line': b'  140 |   RCLCPP_INFO(nh->get_logger(), \x1b[01;35m\x1b[K"Timestamp: %ld.%09ld"\x1b[m\x1b[K,\n'}
[4.145750] (local_planner) StderrLine: {'line': b'      |                                 \x1b[01;35m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[4.145780] (local_planner) StderrLine: {'line': b'  141 |               pathIn->header.stamp.sec, \x1b[32m\x1b[KpathIn->header.stamp.nanosec\x1b[m\x1b[K);\n'}
[4.145808] (local_planner) StderrLine: {'line': b'      |                                         \x1b[32m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[4.145835] (local_planner) StderrLine: {'line': b'      |                                                              \x1b[32m\x1b[K|\x1b[m\x1b[K\n'}
[4.145862] (local_planner) StderrLine: {'line': b'      |                                                              \x1b[32m\x1b[Kunsigned int\x1b[m\x1b[K\n'}
[4.145894] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/src/pathFollower.cpp:140:53:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kformat string is defined here\n'}
[4.145923] (local_planner) StderrLine: {'line': b'  140 |   RCLCPP_INFO(nh->get_logger(), "Timestamp: %ld.\x1b[01;36m\x1b[K%09ld\x1b[m\x1b[K",\n'}
[4.145951] (local_planner) StderrLine: {'line': b'      |                                                 \x1b[01;36m\x1b[K~~~~^\x1b[m\x1b[K\n'}
[4.145981] (local_planner) StderrLine: {'line': b'      |                                                     \x1b[01;36m\x1b[K|\x1b[m\x1b[K\n'}
[4.146012] (local_planner) StderrLine: {'line': b'      |                                                     \x1b[01;36m\x1b[Klong int\x1b[m\x1b[K\n'}
[4.146042] (local_planner) StderrLine: {'line': b'      |                                                 \x1b[32m\x1b[K%09d\x1b[m\x1b[K\n'}
[4.214151] (-) TimerEvent: {}
[4.314543] (-) TimerEvent: {}
[4.414829] (-) TimerEvent: {}
[4.515143] (-) TimerEvent: {}
[4.615586] (-) TimerEvent: {}
[4.715877] (-) TimerEvent: {}
[4.816148] (-) TimerEvent: {}
[4.916542] (-) TimerEvent: {}
[5.016840] (-) TimerEvent: {}
[5.117109] (-) TimerEvent: {}
[5.217410] (-) TimerEvent: {}
[5.317716] (-) TimerEvent: {}
[5.417999] (-) TimerEvent: {}
[5.518314] (-) TimerEvent: {}
[5.618782] (-) TimerEvent: {}
[5.719074] (-) TimerEvent: {}
[5.819468] (-) TimerEvent: {}
[5.919786] (-) TimerEvent: {}
[6.020068] (-) TimerEvent: {}
[6.120444] (-) TimerEvent: {}
[6.220916] (-) TimerEvent: {}
[6.321411] (-) TimerEvent: {}
[6.421657] (-) TimerEvent: {}
[6.521939] (-) TimerEvent: {}
[6.622263] (-) TimerEvent: {}
[6.722563] (-) TimerEvent: {}
[6.823017] (-) TimerEvent: {}
[6.923396] (-) TimerEvent: {}
[7.023725] (-) TimerEvent: {}
[7.124034] (-) TimerEvent: {}
[7.224421] (-) TimerEvent: {}
[7.324810] (-) TimerEvent: {}
[7.425184] (-) TimerEvent: {}
[7.525496] (-) TimerEvent: {}
[7.625780] (-) TimerEvent: {}
[7.726103] (-) TimerEvent: {}
[7.826553] (-) TimerEvent: {}
[7.926913] (-) TimerEvent: {}
[8.027254] (-) TimerEvent: {}
[8.127575] (-) TimerEvent: {}
[8.227871] (-) TimerEvent: {}
[8.328181] (-) TimerEvent: {}
[8.428492] (-) TimerEvent: {}
[8.528784] (-) TimerEvent: {}
[8.629075] (-) TimerEvent: {}
[8.729395] (-) TimerEvent: {}
[8.829663] (-) TimerEvent: {}
[8.929926] (-) TimerEvent: {}
[9.030200] (-) TimerEvent: {}
[9.130512] (-) TimerEvent: {}
[9.230787] (-) TimerEvent: {}
[9.331064] (-) TimerEvent: {}
[9.431393] (-) TimerEvent: {}
[9.531657] (-) TimerEvent: {}
[9.631959] (-) TimerEvent: {}
[9.732341] (-) TimerEvent: {}
[9.832662] (-) TimerEvent: {}
[9.932992] (-) TimerEvent: {}
[10.033327] (-) TimerEvent: {}
[10.133617] (-) TimerEvent: {}
[10.233882] (-) TimerEvent: {}
[10.334188] (-) TimerEvent: {}
[10.434463] (-) TimerEvent: {}
[10.534746] (-) TimerEvent: {}
[10.635047] (-) TimerEvent: {}
[10.735354] (-) TimerEvent: {}
[10.835690] (-) TimerEvent: {}
[10.935991] (-) TimerEvent: {}
[11.036366] (-) TimerEvent: {}
[11.136756] (-) TimerEvent: {}
[11.237061] (-) TimerEvent: {}
[11.337444] (-) TimerEvent: {}
[11.437747] (-) TimerEvent: {}
[11.538039] (-) TimerEvent: {}
[11.638322] (-) TimerEvent: {}
[11.738655] (-) TimerEvent: {}
[11.838961] (-) TimerEvent: {}
[11.939254] (-) TimerEvent: {}
[12.039565] (-) TimerEvent: {}
[12.139899] (-) TimerEvent: {}
[12.240236] (-) TimerEvent: {}
[12.340692] (-) TimerEvent: {}
[12.441058] (-) TimerEvent: {}
[12.541405] (-) TimerEvent: {}
[12.641722] (-) TimerEvent: {}
[12.742056] (-) TimerEvent: {}
[12.842458] (-) TimerEvent: {}
[12.942818] (-) TimerEvent: {}
[13.043152] (-) TimerEvent: {}
[13.143535] (-) TimerEvent: {}
[13.243840] (-) TimerEvent: {}
[13.344150] (-) TimerEvent: {}
[13.444500] (-) TimerEvent: {}
[13.544802] (-) TimerEvent: {}
[13.645102] (-) TimerEvent: {}
[13.745493] (-) TimerEvent: {}
[13.845793] (-) TimerEvent: {}
[13.946071] (-) TimerEvent: {}
[14.046390] (-) TimerEvent: {}
[14.146711] (-) TimerEvent: {}
[14.246987] (-) TimerEvent: {}
[14.347292] (-) TimerEvent: {}
[14.447678] (-) TimerEvent: {}
[14.547969] (-) TimerEvent: {}
[14.648295] (-) TimerEvent: {}
[14.748728] (-) TimerEvent: {}
[14.849006] (-) TimerEvent: {}
[14.949276] (-) TimerEvent: {}
[15.049562] (-) TimerEvent: {}
[15.149841] (-) TimerEvent: {}
[15.250145] (-) TimerEvent: {}
[15.350452] (-) TimerEvent: {}
[15.450747] (-) TimerEvent: {}
[15.551039] (-) TimerEvent: {}
[15.651345] (-) TimerEvent: {}
[15.751648] (-) TimerEvent: {}
[15.851928] (-) TimerEvent: {}
[15.952246] (-) TimerEvent: {}
[16.052564] (-) TimerEvent: {}
[16.152870] (-) TimerEvent: {}
[16.253260] (-) TimerEvent: {}
[16.353567] (-) TimerEvent: {}
[16.453877] (-) TimerEvent: {}
[16.554180] (-) TimerEvent: {}
[16.654485] (-) TimerEvent: {}
[16.754802] (-) TimerEvent: {}
[16.762417] (local_planner) StdoutLine: {'line': b'[ 75%] \x1b[32m\x1b[1mLinking CXX executable pathFollower\x1b[0m\n'}
[16.854903] (-) TimerEvent: {}
[16.955193] (-) TimerEvent: {}
[17.055487] (-) TimerEvent: {}
[17.155791] (-) TimerEvent: {}
[17.256078] (-) TimerEvent: {}
[17.356476] (-) TimerEvent: {}
[17.456819] (-) TimerEvent: {}
[17.557093] (-) TimerEvent: {}
[17.657413] (-) TimerEvent: {}
[17.757768] (-) TimerEvent: {}
[17.858149] (-) TimerEvent: {}
[17.958587] (-) TimerEvent: {}
[18.058917] (-) TimerEvent: {}
[18.141996] (local_planner) StdoutLine: {'line': b'[ 75%] Built target pathFollower\n'}
[18.159049] (-) TimerEvent: {}
[18.259363] (-) TimerEvent: {}
[18.359681] (-) TimerEvent: {}
[18.459997] (-) TimerEvent: {}
[18.560306] (-) TimerEvent: {}
[18.660702] (-) TimerEvent: {}
[18.760974] (-) TimerEvent: {}
[18.861360] (-) TimerEvent: {}
[18.961741] (-) TimerEvent: {}
[19.062147] (-) TimerEvent: {}
[19.162537] (-) TimerEvent: {}
[19.262833] (-) TimerEvent: {}
[19.363161] (-) TimerEvent: {}
[19.463469] (-) TimerEvent: {}
[19.563754] (-) TimerEvent: {}
[19.664043] (-) TimerEvent: {}
[19.764317] (-) TimerEvent: {}
[19.864602] (-) TimerEvent: {}
[19.964876] (-) TimerEvent: {}
[20.065178] (-) TimerEvent: {}
[20.165584] (-) TimerEvent: {}
[20.265902] (-) TimerEvent: {}
[20.366211] (-) TimerEvent: {}
[20.466537] (-) TimerEvent: {}
[20.566882] (-) TimerEvent: {}
[20.667259] (-) TimerEvent: {}
[20.767562] (-) TimerEvent: {}
[20.867855] (-) TimerEvent: {}
[20.876057] (local_planner) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable localPlanner\x1b[0m\n'}
[20.967974] (-) TimerEvent: {}
[21.068273] (-) TimerEvent: {}
[21.168681] (-) TimerEvent: {}
[21.269004] (-) TimerEvent: {}
[21.369300] (-) TimerEvent: {}
[21.469565] (-) TimerEvent: {}
[21.569871] (-) TimerEvent: {}
[21.670227] (-) TimerEvent: {}
[21.770679] (-) TimerEvent: {}
[21.871030] (-) TimerEvent: {}
[21.971468] (-) TimerEvent: {}
[22.071914] (-) TimerEvent: {}
[22.172311] (-) TimerEvent: {}
[22.272691] (-) TimerEvent: {}
[22.373077] (-) TimerEvent: {}
[22.473538] (-) TimerEvent: {}
[22.574013] (-) TimerEvent: {}
[22.674374] (-) TimerEvent: {}
[22.774887] (-) TimerEvent: {}
[22.817328] (local_planner) StdoutLine: {'line': b'[100%] Built target localPlanner\n'}
[22.835898] (local_planner) CommandEnded: {'returncode': 0}
[22.837629] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'install'}
[22.852390] (local_planner) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner'], 'cwd': '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/saoxueche0808pm/saoxueche'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1721'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '2255'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator23558193cd9818af7fe4d2c2f5bd9d00f'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3954'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:21070'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('CLAUDE_CODE_SSE_PORT', '55520'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/2006,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/2006'), ('INVOCATION_ID', '66d5ccea36d5493290db9ab4d0365f53'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-1a39ff7581ade6bb.txt'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-dcb10a45d5.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:6ae87ae5-7baf-4b26-89ab-8d76be68d48f'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner'), ('ENABLE_IDE_INTEGRATION', 'true'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[22.865880] (local_planner) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[22.866996] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/localPlanner\n'}
[22.874990] (-) TimerEvent: {}
[22.881429] (local_planner) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/localPlanner" to ""\n'}
[22.882608] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/pathFollower\n'}
[22.889494] (local_planner) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/local_planner/pathFollower" to ""\n'}
[22.890009] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch\n'}
[22.890065] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch/local_planner.launch.py\n'}
[22.890100] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/launch/local_planner.launch\n'}
[22.890132] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths\n'}
[22.890163] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/startPaths.ply\n'}
[22.890204] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/correspondences.txt\n'}
[22.890248] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/pathList.ply\n'}
[22.890332] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/paths.ply\n'}
[22.890373] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/paths/path_generator.m\n'}
[22.890414] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/config\n'}
[22.890455] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/package_run_dependencies/local_planner\n'}
[22.890494] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/parent_prefix_path/local_planner\n'}
[22.890533] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/ament_prefix_path.sh\n'}
[22.890571] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/ament_prefix_path.dsv\n'}
[22.890618] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/path.sh\n'}
[22.890661] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/environment/path.dsv\n'}
[22.890700] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.bash\n'}
[22.890739] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.sh\n'}
[22.890784] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.zsh\n'}
[22.890823] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/local_setup.dsv\n'}
[22.890860] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv\n'}
[22.890899] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/ament_index/resource_index/packages/local_planner\n'}
[22.890936] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/cmake/local_plannerConfig.cmake\n'}
[22.890973] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/cmake/local_plannerConfig-version.cmake\n'}
[22.891009] (local_planner) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.xml\n'}
[22.892304] (local_planner) CommandEnded: {'returncode': 0}
[22.904289] (local_planner) JobEnded: {'identifier': 'local_planner', 'rc': 0}
[22.904798] (-) EventReactorShutdown: {}
