[0.068s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'local_planner']
[0.069s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['local_planner'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x70f301554f70>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x70f301554b20>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x70f301554b20>>)
[0.191s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.191s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.191s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.191s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.191s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.191s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.191s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/saoxueche0808pm/saoxueche'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'ignore_ament_install'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['colcon_pkg']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'colcon_pkg'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['colcon_meta']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'colcon_meta'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['ros']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'ros'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['cmake', 'python']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'cmake'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'python'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extensions ['python_setup_py']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(output.bag) by extension 'python_setup_py'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ignore_ament_install'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['colcon_pkg']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'colcon_pkg'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['colcon_meta']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'colcon_meta'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extensions ['ros']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/integrated_navigation) by extension 'ros'
[0.205s] DEBUG:colcon.colcon_core.package_identification:Package 'src/integrated_navigation' with type 'ros.ament_cmake' and name 'integrated_navigation'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['ignore', 'ignore_ament_install']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ignore'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ignore_ament_install'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['colcon_pkg']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'colcon_pkg'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['colcon_meta']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'colcon_meta'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extensions ['ros']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/loam_interface) by extension 'ros'
[0.206s] DEBUG:colcon.colcon_core.package_identification:Package 'src/loam_interface' with type 'ros.ament_cmake' and name 'loam_interface'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ignore', 'ignore_ament_install']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore_ament_install'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_pkg']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_pkg'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_meta']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_meta'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ros']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ros'
[0.207s] DEBUG:colcon.colcon_core.package_identification:Package 'src/local_planner' with type 'ros.ament_cmake' and name 'local_planner'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['ignore', 'ignore_ament_install']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ignore'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ignore_ament_install'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['colcon_pkg']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'colcon_pkg'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['colcon_meta']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'colcon_meta'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['ros']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ros'
[0.209s] DEBUG:colcon.colcon_core.package_identification:Package 'src/nav2_smac_test' with type 'ros.ament_cmake' and name 'nav2_smac_test'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['ignore', 'ignore_ament_install']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ignore'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ignore_ament_install'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['colcon_pkg']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'colcon_pkg'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['colcon_meta']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'colcon_meta'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extensions ['ros']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(src/sensor_scan_generation) by extension 'ros'
[0.210s] DEBUG:colcon.colcon_core.package_identification:Package 'src/sensor_scan_generation' with type 'ros.ament_cmake' and name 'sensor_scan_generation'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ignore', 'ignore_ament_install']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore_ament_install'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_pkg']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_pkg'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_meta']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_meta'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ros']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ros'
[0.211s] DEBUG:colcon.colcon_core.package_identification:Package 'src/terrain_analysis' with type 'ros.ament_cmake' and name 'terrain_analysis'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['ignore', 'ignore_ament_install']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ignore'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ignore_ament_install'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['colcon_pkg']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'colcon_pkg'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['colcon_meta']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'colcon_meta'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extensions ['ros']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis_ext) by extension 'ros'
[0.212s] DEBUG:colcon.colcon_core.package_identification:Package 'src/terrain_analysis_ext' with type 'ros.ament_cmake' and name 'terrain_analysis_ext'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) by extension 'ignore'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/vehicle_simulator) ignored
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extensions ['ignore', 'ignore_ament_install']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) by extension 'ignore'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(src/velodyne_simulator) ignored
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['ignore', 'ignore_ament_install']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ignore'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ignore_ament_install'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['colcon_pkg']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'colcon_pkg'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['colcon_meta']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'colcon_meta'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extensions ['ros']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/visualization_tools) by extension 'ros'
[0.214s] DEBUG:colcon.colcon_core.package_identification:Package 'src/visualization_tools' with type 'ros.ament_cmake' and name 'visualization_tools'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['ignore', 'ignore_ament_install']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ignore'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ignore_ament_install'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['colcon_pkg']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'colcon_pkg'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['colcon_meta']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'colcon_meta'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extensions ['ros']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_example) by extension 'ros'
[0.215s] DEBUG:colcon.colcon_core.package_identification:Package 'src/waypoint_example' with type 'ros.ament_cmake' and name 'waypoint_example'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['ignore', 'ignore_ament_install']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ignore'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ignore_ament_install'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['colcon_pkg']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'colcon_pkg'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['colcon_meta']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'colcon_meta'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extensions ['ros']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/waypoint_rviz_plugin) by extension 'ros'
[0.216s] DEBUG:colcon.colcon_core.package_identification:Package 'src/waypoint_rviz_plugin' with type 'ros.ament_cmake' and name 'waypoint_rviz_plugin'
[0.216s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.216s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.216s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.216s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.216s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.238s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'integrated_navigation' in 'src/integrated_navigation'
[0.238s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'loam_interface' in 'src/loam_interface'
[0.239s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'nav2_smac_test' in 'src/nav2_smac_test'
[0.239s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'sensor_scan_generation' in 'src/sensor_scan_generation'
[0.239s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'terrain_analysis' in 'src/terrain_analysis'
[0.239s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'terrain_analysis_ext' in 'src/terrain_analysis_ext'
[0.239s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'visualization_tools' in 'src/visualization_tools'
[0.239s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'waypoint_example' in 'src/waypoint_example'
[0.239s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'waypoint_rviz_plugin' in 'src/waypoint_rviz_plugin'
[0.239s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.239s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.241s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 473 installed packages in /opt/ros/humble
[0.242s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.275s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_args' from command line to 'None'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target' from command line to 'None'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.275s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.275s] DEBUG:colcon.colcon_core.verb:Building package 'local_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner', 'merge_install': False, 'path': '/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner', 'symlink_install': False, 'test_result_base': None}
[0.275s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.276s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.276s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner' with build type 'ament_cmake'
[0.277s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner'
[0.278s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.278s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.278s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.286s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner -- -j16 -l16
[23.113s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner -- -j16 -l16
[23.129s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner
[23.168s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[23.168s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/saoxueche0808pm/saoxueche/build/local_planner
[23.171s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake module files
[23.171s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake config files
[23.171s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[23.172s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[23.172s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[23.173s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[23.173s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib'
[23.173s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[23.174s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/pkgconfig/local_planner.pc'
[23.174s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/python3.10/site-packages'
[23.174s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[23.174s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.ps1'
[23.175s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv'
[23.175s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.sh'
[23.175s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.bash'
[23.176s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.zsh'
[23.177s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/colcon-core/packages/local_planner)
[23.177s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[23.177s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake module files
[23.177s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner' for CMake config files
[23.177s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[23.178s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[23.178s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[23.178s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[23.178s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib'
[23.178s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[23.178s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/pkgconfig/local_planner.pc'
[23.179s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/lib/python3.10/site-packages'
[23.179s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/bin'
[23.179s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.ps1'
[23.179s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.dsv'
[23.179s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.sh'
[23.180s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.bash'
[23.180s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/local_planner/package.zsh'
[23.180s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner/share/colcon-core/packages/local_planner)
[23.180s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[23.180s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[23.180s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[23.180s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[23.185s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[23.185s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[23.185s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[23.196s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[23.197s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.ps1'
[23.198s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/saoxueche0808pm/saoxueche/install/_local_setup_util_ps1.py'
[23.198s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.ps1'
[23.199s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.sh'
[23.200s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/saoxueche0808pm/saoxueche/install/_local_setup_util_sh.py'
[23.200s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.sh'
[23.201s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.bash'
[23.201s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.bash'
[23.202s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/saoxueche0808pm/saoxueche/install/local_setup.zsh'
[23.202s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/saoxueche0808pm/saoxueche/install/setup.zsh'
