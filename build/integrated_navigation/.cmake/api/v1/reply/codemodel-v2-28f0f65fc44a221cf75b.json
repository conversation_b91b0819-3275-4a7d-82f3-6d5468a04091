{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-e1a0b64ac451d0fa1605.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "integrated_navigation", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "integrated_navigation_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-integrated_navigation_uninstall-3ef2041a2090011d9c1a.json", "name": "integrated_navigation_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-0275d03ddafbfb42ca90.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/saoxueche0808pm/saoxueche/build/integrated_navigation", "source": "/home/<USER>/saoxueche0808pm/saoxueche/src/integrated_navigation"}, "version": {"major": 2, "minor": 3}}