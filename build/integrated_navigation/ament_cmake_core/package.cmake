set(_AMENT_PACKAGE_NAME "integrated_navigation")
set(integrated_navigation_VERSION "1.0.0")
set(integrated_navigation_MAINTAINER "User <<EMAIL>>")
set(integrated_navigation_BUILD_DEPENDS "nav2_bringup" "nav2_common" "nav2_smac_planner" "nav2_planner" "nav2_controller" "nav2_costmap_2d" "nav2_lifecycle_manager" "nav2_map_server" "nav2_amcl" "nav2_bt_navigator" "navigation2" "gazebo_ros_pkgs" "robot_state_publisher" "xacro" "tf2" "tf2_ros" "tf2_geometry_msgs" "geometry_msgs" "sensor_msgs" "std_msgs" "nav_msgs" "rclcpp" "rclpy" "pcl_ros" "pcl_conversions" "message_filters")
set(integrated_navigation_BUILDTOOL_DEPENDS "ament_cmake")
set(integrated_navigation_BUILD_EXPORT_DEPENDS "nav2_bringup" "nav2_common" "nav2_smac_planner" "nav2_planner" "nav2_controller" "nav2_costmap_2d" "nav2_lifecycle_manager" "nav2_map_server" "nav2_amcl" "nav2_bt_navigator" "navigation2" "gazebo_ros_pkgs" "robot_state_publisher" "xacro" "tf2" "tf2_ros" "tf2_geometry_msgs" "geometry_msgs" "sensor_msgs" "std_msgs" "nav_msgs" "rclcpp" "rclpy" "pcl_ros" "pcl_conversions" "message_filters")
set(integrated_navigation_BUILDTOOL_EXPORT_DEPENDS )
set(integrated_navigation_EXEC_DEPENDS "nav2_bringup" "nav2_common" "nav2_smac_planner" "nav2_planner" "nav2_controller" "nav2_costmap_2d" "nav2_lifecycle_manager" "nav2_map_server" "nav2_amcl" "nav2_bt_navigator" "navigation2" "gazebo_ros_pkgs" "robot_state_publisher" "xacro" "tf2" "tf2_ros" "tf2_geometry_msgs" "geometry_msgs" "sensor_msgs" "std_msgs" "nav_msgs" "rclcpp" "rclpy" "pcl_ros" "pcl_conversions" "message_filters")
set(integrated_navigation_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(integrated_navigation_GROUP_DEPENDS )
set(integrated_navigation_MEMBER_OF_GROUPS )
set(integrated_navigation_DEPRECATED "")
set(integrated_navigation_EXPORT_TAGS)
list(APPEND integrated_navigation_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
