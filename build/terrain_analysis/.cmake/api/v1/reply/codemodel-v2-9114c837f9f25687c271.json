{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-95d3bf9f4233d4e3c979.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "terrain_analysis", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "terrainAnalysis::@6890427a1f51a3e7e1df", "jsonFile": "target-terrainAnalysis-91c2352ef3e6ffb4541c.json", "name": "terrainAnalysis", "projectIndex": 0}, {"directoryIndex": 0, "id": "terrain_analysis_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-terrain_analysis_uninstall-527a1e4de05b5f272244.json", "name": "terrain_analysis_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-ae228557680637569ca3.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis", "source": "/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis"}, "version": {"major": 2, "minor": 3}}