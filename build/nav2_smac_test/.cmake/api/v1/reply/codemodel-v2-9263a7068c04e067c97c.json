{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-897b5bdc77605c81021e.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "nav2_smac_test", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "nav2_smac_test_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-nav2_smac_test_uninstall-66ec7fd25c2290a2591e.json", "name": "nav2_smac_test_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-497d80e059c57c1b3765.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test", "source": "/home/<USER>/saoxueche0808pm/saoxueche/src/nav2_smac_test"}, "version": {"major": 2, "minor": 3}}