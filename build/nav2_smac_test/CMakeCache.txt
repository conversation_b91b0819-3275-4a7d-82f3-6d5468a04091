# This is the CMakeCache file.
# For build in directory: /home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Generate environment files in the CMAKE_INSTALL_PREFIX
AMENT_CMAKE_ENVIRONMENT_GENERATION:BOOL=OFF

//Generate environment files in the package share folder
AMENT_CMAKE_ENVIRONMENT_PACKAGE_GENERATION:BOOL=ON

//Generate marker file containing the parent prefix path
AMENT_CMAKE_ENVIRONMENT_PARENT_PREFIX_PATH_GENERATION:BOOL=ON

//Replace the CMake install command with a custom implementation
// using symlinks instead of copying resources
AMENT_CMAKE_SYMLINK_INSTALL:BOOL=OFF

//Generate an uninstall target to revert the effects of the install
// step
AMENT_CMAKE_UNINSTALL_TARGET:BOOL=ON

//The path where test results are generated
AMENT_TEST_RESULTS_DIR:PATH=/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test/test_results

//Build the testing tree.
BUILD_TESTING:BOOL=ON

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-11

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-11

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-11

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-11

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/home/<USER>/saoxueche0808pm/saoxueche/install/nav2_smac_test

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/gmake

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=nav2_smac_test

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//The directory containing a CMake configuration file for Eigen3.
Eigen3_DIR:PATH=/usr/share/eigen3/cmake

//Path to a library.
FastCDR_LIBRARY_DEBUG:FILEPATH=FastCDR_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
FastCDR_LIBRARY_RELEASE:FILEPATH=/opt/ros/humble/lib/libfastcdr.so

//Path to a file.
FastRTPS_INCLUDE_DIR:PATH=/opt/ros/humble/include

//Path to a library.
FastRTPS_LIBRARY_DEBUG:FILEPATH=FastRTPS_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
FastRTPS_LIBRARY_RELEASE:FILEPATH=/opt/ros/humble/lib/libfastrtps.so

//Path to a library.
OPENSSL_CRYPTO_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcrypto.so

//Path to a file.
OPENSSL_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
OPENSSL_SSL_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libssl.so

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Path to a program.
Python3_EXECUTABLE:FILEPATH=/usr/bin/python3

//Name of the computer/site where compile is being run
SITE:STRING=hy-Inspiron-16-Plus-7640

//Path to a file.
TINYXML2_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
TINYXML2_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libtinyxml2.so

//The directory containing a CMake configuration file for TinyXML2.
TinyXML2_DIR:PATH=TinyXML2_DIR-NOTFOUND

//Path to a library.
_lib:FILEPATH=/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_fastrtps_cpp.so

//The directory containing a CMake configuration file for action_msgs.
action_msgs_DIR:PATH=/opt/ros/humble/share/action_msgs/cmake

//The directory containing a CMake configuration file for ament_cmake.
ament_cmake_DIR:PATH=/opt/ros/humble/share/ament_cmake/cmake

//The directory containing a CMake configuration file for ament_cmake_copyright.
ament_cmake_copyright_DIR:PATH=/opt/ros/humble/share/ament_cmake_copyright/cmake

//The directory containing a CMake configuration file for ament_cmake_core.
ament_cmake_core_DIR:PATH=/opt/ros/humble/share/ament_cmake_core/cmake

//The directory containing a CMake configuration file for ament_cmake_cppcheck.
ament_cmake_cppcheck_DIR:PATH=/opt/ros/humble/share/ament_cmake_cppcheck/cmake

//The directory containing a CMake configuration file for ament_cmake_cpplint.
ament_cmake_cpplint_DIR:PATH=/opt/ros/humble/share/ament_cmake_cpplint/cmake

//The directory containing a CMake configuration file for ament_cmake_export_definitions.
ament_cmake_export_definitions_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_definitions/cmake

//The directory containing a CMake configuration file for ament_cmake_export_dependencies.
ament_cmake_export_dependencies_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_dependencies/cmake

//The directory containing a CMake configuration file for ament_cmake_export_include_directories.
ament_cmake_export_include_directories_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_include_directories/cmake

//The directory containing a CMake configuration file for ament_cmake_export_interfaces.
ament_cmake_export_interfaces_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_interfaces/cmake

//The directory containing a CMake configuration file for ament_cmake_export_libraries.
ament_cmake_export_libraries_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_libraries/cmake

//The directory containing a CMake configuration file for ament_cmake_export_link_flags.
ament_cmake_export_link_flags_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_link_flags/cmake

//The directory containing a CMake configuration file for ament_cmake_export_targets.
ament_cmake_export_targets_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_targets/cmake

//The directory containing a CMake configuration file for ament_cmake_flake8.
ament_cmake_flake8_DIR:PATH=/opt/ros/humble/share/ament_cmake_flake8/cmake

//The directory containing a CMake configuration file for ament_cmake_gen_version_h.
ament_cmake_gen_version_h_DIR:PATH=/opt/ros/humble/share/ament_cmake_gen_version_h/cmake

//The directory containing a CMake configuration file for ament_cmake_include_directories.
ament_cmake_include_directories_DIR:PATH=/opt/ros/humble/share/ament_cmake_include_directories/cmake

//The directory containing a CMake configuration file for ament_cmake_libraries.
ament_cmake_libraries_DIR:PATH=/opt/ros/humble/share/ament_cmake_libraries/cmake

//The directory containing a CMake configuration file for ament_cmake_lint_cmake.
ament_cmake_lint_cmake_DIR:PATH=/opt/ros/humble/share/ament_cmake_lint_cmake/cmake

//The directory containing a CMake configuration file for ament_cmake_pep257.
ament_cmake_pep257_DIR:PATH=/opt/ros/humble/share/ament_cmake_pep257/cmake

//The directory containing a CMake configuration file for ament_cmake_python.
ament_cmake_python_DIR:PATH=/opt/ros/humble/share/ament_cmake_python/cmake

//The directory containing a CMake configuration file for ament_cmake_target_dependencies.
ament_cmake_target_dependencies_DIR:PATH=/opt/ros/humble/share/ament_cmake_target_dependencies/cmake

//The directory containing a CMake configuration file for ament_cmake_test.
ament_cmake_test_DIR:PATH=/opt/ros/humble/share/ament_cmake_test/cmake

//The directory containing a CMake configuration file for ament_cmake_uncrustify.
ament_cmake_uncrustify_DIR:PATH=/opt/ros/humble/share/ament_cmake_uncrustify/cmake

//The directory containing a CMake configuration file for ament_cmake_version.
ament_cmake_version_DIR:PATH=/opt/ros/humble/share/ament_cmake_version/cmake

//The directory containing a CMake configuration file for ament_cmake_xmllint.
ament_cmake_xmllint_DIR:PATH=/opt/ros/humble/share/ament_cmake_xmllint/cmake

//Path to a program.
ament_copyright_BIN:FILEPATH=/opt/ros/humble/bin/ament_copyright

//Path to a program.
ament_flake8_BIN:FILEPATH=/opt/ros/humble/bin/ament_flake8

//The directory containing a CMake configuration file for ament_index_cpp.
ament_index_cpp_DIR:PATH=/opt/ros/humble/share/ament_index_cpp/cmake

//The directory containing a CMake configuration file for ament_lint_auto.
ament_lint_auto_DIR:PATH=/opt/ros/humble/share/ament_lint_auto/cmake

//Path to a program.
ament_lint_cmake_BIN:FILEPATH=/opt/ros/humble/bin/ament_lint_cmake

//The directory containing a CMake configuration file for ament_lint_common.
ament_lint_common_DIR:PATH=/opt/ros/humble/share/ament_lint_common/cmake

//Path to a program.
ament_pep257_BIN:FILEPATH=/opt/ros/humble/bin/ament_pep257

//Path to a program.
ament_xmllint_BIN:FILEPATH=/opt/ros/humble/bin/ament_xmllint

//The directory containing a CMake configuration file for angles.
angles_DIR:PATH=/opt/ros/humble/share/angles/cmake

//The directory containing a CMake configuration file for bond.
bond_DIR:PATH=/opt/ros/humble/share/bond/cmake

//The directory containing a CMake configuration file for bondcpp.
bondcpp_DIR:PATH=/opt/ros/humble/share/bondcpp/cmake

//The directory containing a CMake configuration file for builtin_interfaces.
builtin_interfaces_DIR:PATH=/opt/ros/humble/share/builtin_interfaces/cmake

//The directory containing a CMake configuration file for class_loader.
class_loader_DIR:PATH=/opt/ros/humble/share/class_loader/cmake

//The directory containing a CMake configuration file for composition_interfaces.
composition_interfaces_DIR:PATH=/opt/ros/humble/share/composition_interfaces/cmake

//The directory containing a CMake configuration file for console_bridge.
console_bridge_DIR:PATH=/usr/lib/x86_64-linux-gnu/console_bridge/cmake

//The directory containing a CMake configuration file for eigen3_cmake_module.
eigen3_cmake_module_DIR:PATH=/opt/ros/humble/share/eigen3_cmake_module/cmake

//The directory containing a CMake configuration file for fastcdr.
fastcdr_DIR:PATH=/opt/ros/humble/lib/cmake/fastcdr

//The directory containing a CMake configuration file for fastrtps.
fastrtps_DIR:PATH=/opt/ros/humble/share/fastrtps/cmake

//The directory containing a CMake configuration file for fastrtps_cmake_module.
fastrtps_cmake_module_DIR:PATH=/opt/ros/humble/share/fastrtps_cmake_module/cmake

//The directory containing a CMake configuration file for fmt.
fmt_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/fmt

//The directory containing a CMake configuration file for foonathan_memory.
foonathan_memory_DIR:PATH=/opt/ros/humble/lib/foonathan_memory/cmake

//The directory containing a CMake configuration file for geometry_msgs.
geometry_msgs_DIR:PATH=/opt/ros/humble/share/geometry_msgs/cmake

//The directory containing a CMake configuration file for laser_geometry.
laser_geometry_DIR:PATH=/opt/ros/humble/share/laser_geometry/cmake

//The directory containing a CMake configuration file for libstatistics_collector.
libstatistics_collector_DIR:PATH=/opt/ros/humble/share/libstatistics_collector/cmake

//The directory containing a CMake configuration file for libyaml_vendor.
libyaml_vendor_DIR:PATH=/opt/ros/humble/share/libyaml_vendor/cmake

//The directory containing a CMake configuration file for lifecycle_msgs.
lifecycle_msgs_DIR:PATH=/opt/ros/humble/share/lifecycle_msgs/cmake

//The directory containing a CMake configuration file for map_msgs.
map_msgs_DIR:PATH=/opt/ros/humble/share/map_msgs/cmake

//The directory containing a CMake configuration file for message_filters.
message_filters_DIR:PATH=/opt/ros/humble/share/message_filters/cmake

//The directory containing a CMake configuration file for nav2_common.
nav2_common_DIR:PATH=/opt/ros/humble/share/nav2_common/cmake

//The directory containing a CMake configuration file for nav2_core.
nav2_core_DIR:PATH=/opt/ros/humble/share/nav2_core/cmake

//The directory containing a CMake configuration file for nav2_costmap_2d.
nav2_costmap_2d_DIR:PATH=/opt/ros/humble/share/nav2_costmap_2d/cmake

//The directory containing a CMake configuration file for nav2_msgs.
nav2_msgs_DIR:PATH=/opt/ros/humble/share/nav2_msgs/cmake

//The directory containing a CMake configuration file for nav2_smac_planner.
nav2_smac_planner_DIR:PATH=/opt/ros/humble/share/nav2_smac_planner/cmake

//Value Computed by CMake
nav2_smac_test_BINARY_DIR:STATIC=/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test

//Value Computed by CMake
nav2_smac_test_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
nav2_smac_test_SOURCE_DIR:STATIC=/home/<USER>/saoxueche0808pm/saoxueche/src/nav2_smac_test

//The directory containing a CMake configuration file for nav2_util.
nav2_util_DIR:PATH=/opt/ros/humble/share/nav2_util/cmake

//The directory containing a CMake configuration file for nav2_voxel_grid.
nav2_voxel_grid_DIR:PATH=/opt/ros/humble/share/nav2_voxel_grid/cmake

//The directory containing a CMake configuration file for nav_msgs.
nav_msgs_DIR:PATH=/opt/ros/humble/share/nav_msgs/cmake

//The directory containing a CMake configuration file for navigation2.
navigation2_DIR:PATH=/opt/ros/humble/share/navigation2/cmake

//The directory containing a CMake configuration file for orocos_kdl.
orocos_kdl_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/orocos_kdl

//Path to a library.
orocos_kdl_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/liborocos-kdl.so

//The directory containing a CMake configuration file for orocos_kdl_vendor.
orocos_kdl_vendor_DIR:PATH=/opt/ros/humble/share/orocos_kdl_vendor/cmake

//Path to a library.
pkgcfg_lib__OPENSSL_crypto:FILEPATH=/usr/lib/x86_64-linux-gnu/libcrypto.so

//Path to a library.
pkgcfg_lib__OPENSSL_ssl:FILEPATH=/usr/lib/x86_64-linux-gnu/libssl.so

//The directory containing a CMake configuration file for pluginlib.
pluginlib_DIR:PATH=/opt/ros/humble/share/pluginlib/cmake

//The directory containing a CMake configuration file for rcl.
rcl_DIR:PATH=/opt/ros/humble/share/rcl/cmake

//The directory containing a CMake configuration file for rcl_action.
rcl_action_DIR:PATH=/opt/ros/humble/share/rcl_action/cmake

//The directory containing a CMake configuration file for rcl_interfaces.
rcl_interfaces_DIR:PATH=/opt/ros/humble/share/rcl_interfaces/cmake

//The directory containing a CMake configuration file for rcl_lifecycle.
rcl_lifecycle_DIR:PATH=/opt/ros/humble/share/rcl_lifecycle/cmake

//The directory containing a CMake configuration file for rcl_logging_interface.
rcl_logging_interface_DIR:PATH=/opt/ros/humble/share/rcl_logging_interface/cmake

//The directory containing a CMake configuration file for rcl_logging_spdlog.
rcl_logging_spdlog_DIR:PATH=/opt/ros/humble/share/rcl_logging_spdlog/cmake

//The directory containing a CMake configuration file for rcl_yaml_param_parser.
rcl_yaml_param_parser_DIR:PATH=/opt/ros/humble/share/rcl_yaml_param_parser/cmake

//The directory containing a CMake configuration file for rclcpp.
rclcpp_DIR:PATH=/opt/ros/humble/share/rclcpp/cmake

//The directory containing a CMake configuration file for rclcpp_action.
rclcpp_action_DIR:PATH=/opt/ros/humble/share/rclcpp_action/cmake

//The directory containing a CMake configuration file for rclcpp_components.
rclcpp_components_DIR:PATH=/opt/ros/humble/share/rclcpp_components/cmake

//The directory containing a CMake configuration file for rclcpp_lifecycle.
rclcpp_lifecycle_DIR:PATH=/opt/ros/humble/share/rclcpp_lifecycle/cmake

//The directory containing a CMake configuration file for rcpputils.
rcpputils_DIR:PATH=/opt/ros/humble/share/rcpputils/cmake

//The directory containing a CMake configuration file for rcutils.
rcutils_DIR:PATH=/opt/ros/humble/share/rcutils/cmake

//The directory containing a CMake configuration file for rmw.
rmw_DIR:PATH=/opt/ros/humble/share/rmw/cmake

//The directory containing a CMake configuration file for rmw_dds_common.
rmw_dds_common_DIR:PATH=/opt/ros/humble/share/rmw_dds_common/cmake

//The directory containing a CMake configuration file for rmw_fastrtps_cpp.
rmw_fastrtps_cpp_DIR:PATH=/opt/ros/humble/share/rmw_fastrtps_cpp/cmake

//The directory containing a CMake configuration file for rmw_fastrtps_shared_cpp.
rmw_fastrtps_shared_cpp_DIR:PATH=/opt/ros/humble/share/rmw_fastrtps_shared_cpp/cmake

//The directory containing a CMake configuration file for rmw_implementation.
rmw_implementation_DIR:PATH=/opt/ros/humble/share/rmw_implementation/cmake

//The directory containing a CMake configuration file for rmw_implementation_cmake.
rmw_implementation_cmake_DIR:PATH=/opt/ros/humble/share/rmw_implementation_cmake/cmake

//The directory containing a CMake configuration file for rosgraph_msgs.
rosgraph_msgs_DIR:PATH=/opt/ros/humble/share/rosgraph_msgs/cmake

//The directory containing a CMake configuration file for rosidl_adapter.
rosidl_adapter_DIR:PATH=/opt/ros/humble/share/rosidl_adapter/cmake

//The directory containing a CMake configuration file for rosidl_cmake.
rosidl_cmake_DIR:PATH=/opt/ros/humble/share/rosidl_cmake/cmake

//The directory containing a CMake configuration file for rosidl_default_runtime.
rosidl_default_runtime_DIR:PATH=/opt/ros/humble/share/rosidl_default_runtime/cmake

//The directory containing a CMake configuration file for rosidl_generator_c.
rosidl_generator_c_DIR:PATH=/opt/ros/humble/share/rosidl_generator_c/cmake

//The directory containing a CMake configuration file for rosidl_generator_cpp.
rosidl_generator_cpp_DIR:PATH=/opt/ros/humble/share/rosidl_generator_cpp/cmake

//The directory containing a CMake configuration file for rosidl_runtime_c.
rosidl_runtime_c_DIR:PATH=/opt/ros/humble/share/rosidl_runtime_c/cmake

//The directory containing a CMake configuration file for rosidl_runtime_cpp.
rosidl_runtime_cpp_DIR:PATH=/opt/ros/humble/share/rosidl_runtime_cpp/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_c.
rosidl_typesupport_c_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_c/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_cpp.
rosidl_typesupport_cpp_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_cpp/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_fastrtps_c.
rosidl_typesupport_fastrtps_c_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_fastrtps_cpp.
rosidl_typesupport_fastrtps_cpp_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_fastrtps_cpp/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_interface.
rosidl_typesupport_interface_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_interface/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_introspection_c.
rosidl_typesupport_introspection_c_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_introspection_c/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_introspection_cpp.
rosidl_typesupport_introspection_cpp_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_introspection_cpp/cmake

//The directory containing a CMake configuration file for sensor_msgs.
sensor_msgs_DIR:PATH=/opt/ros/humble/share/sensor_msgs/cmake

//The directory containing a CMake configuration file for smclib.
smclib_DIR:PATH=/opt/ros/humble/share/smclib/cmake

//The directory containing a CMake configuration file for spdlog.
spdlog_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/spdlog

//The directory containing a CMake configuration file for spdlog_vendor.
spdlog_vendor_DIR:PATH=/opt/ros/humble/share/spdlog_vendor/cmake

//The directory containing a CMake configuration file for statistics_msgs.
statistics_msgs_DIR:PATH=/opt/ros/humble/share/statistics_msgs/cmake

//The directory containing a CMake configuration file for std_msgs.
std_msgs_DIR:PATH=/opt/ros/humble/share/std_msgs/cmake

//The directory containing a CMake configuration file for std_srvs.
std_srvs_DIR:PATH=/opt/ros/humble/share/std_srvs/cmake

//The directory containing a CMake configuration file for tf2.
tf2_DIR:PATH=/opt/ros/humble/share/tf2/cmake

//The directory containing a CMake configuration file for tf2_geometry_msgs.
tf2_geometry_msgs_DIR:PATH=/opt/ros/humble/share/tf2_geometry_msgs/cmake

//The directory containing a CMake configuration file for tf2_msgs.
tf2_msgs_DIR:PATH=/opt/ros/humble/share/tf2_msgs/cmake

//The directory containing a CMake configuration file for tf2_ros.
tf2_ros_DIR:PATH=/opt/ros/humble/share/tf2_ros/cmake

//The directory containing a CMake configuration file for tf2_sensor_msgs.
tf2_sensor_msgs_DIR:PATH=/opt/ros/humble/share/tf2_sensor_msgs/cmake

//The directory containing a CMake configuration file for tinyxml2_vendor.
tinyxml2_vendor_DIR:PATH=/opt/ros/humble/share/tinyxml2_vendor/cmake

//The directory containing a CMake configuration file for tracetools.
tracetools_DIR:PATH=/opt/ros/humble/share/tracetools/cmake

//The directory containing a CMake configuration file for unique_identifier_msgs.
unique_identifier_msgs_DIR:PATH=/opt/ros/humble/share/unique_identifier_msgs/cmake

//The directory containing a CMake configuration file for visualization_msgs.
visualization_msgs_DIR:PATH=/opt/ros/humble/share/visualization_msgs/cmake

//Path to a program.
xmllint_BIN:FILEPATH=/usr/bin/xmllint

//The directory containing a CMake configuration file for yaml.
yaml_DIR:PATH=/opt/ros/humble/cmake


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/saoxueche0808pm/saoxueche/build/nav2_smac_test
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=22
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=1
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Have include pthread.h
CMAKE_HAVE_PTHREAD_H:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/saoxueche0808pm/saoxueche/src/nav2_smac_test
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake-3.22
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding Eigen3
FIND_PACKAGE_MESSAGE_DETAILS_Eigen3:INTERNAL=[1][v3.4.0()]
//Details about finding FastRTPS
FIND_PACKAGE_MESSAGE_DETAILS_FastRTPS:INTERNAL=[/opt/ros/humble/include][/opt/ros/humble/lib/libfastrtps.so;/opt/ros/humble/lib/libfastcdr.so][v()]
//Details about finding OpenSSL
FIND_PACKAGE_MESSAGE_DETAILS_OpenSSL:INTERNAL=[/usr/lib/x86_64-linux-gnu/libcrypto.so][/usr/include][c ][v3.0.2()]
//Details about finding Python3
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[/usr/bin/python3][cfound components: Interpreter ][v3.10.12()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//ADVANCED property for variable: OPENSSL_CRYPTO_LIBRARY
OPENSSL_CRYPTO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_INCLUDE_DIR
OPENSSL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_SSL_LIBRARY
OPENSSL_SSL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: TINYXML2_INCLUDE_DIR
TINYXML2_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: TINYXML2_LIBRARY
TINYXML2_LIBRARY-ADVANCED:INTERNAL=1
_OPENSSL_CFLAGS:INTERNAL=
_OPENSSL_CFLAGS_I:INTERNAL=
_OPENSSL_CFLAGS_OTHER:INTERNAL=
_OPENSSL_FOUND:INTERNAL=1
_OPENSSL_INCLUDEDIR:INTERNAL=/usr/include
_OPENSSL_INCLUDE_DIRS:INTERNAL=
_OPENSSL_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lssl;-lcrypto
_OPENSSL_LDFLAGS_OTHER:INTERNAL=
_OPENSSL_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
_OPENSSL_LIBRARIES:INTERNAL=ssl;crypto
_OPENSSL_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
_OPENSSL_LIBS:INTERNAL=
_OPENSSL_LIBS_L:INTERNAL=
_OPENSSL_LIBS_OTHER:INTERNAL=
_OPENSSL_LIBS_PATHS:INTERNAL=
_OPENSSL_MODULE_NAME:INTERNAL=openssl
_OPENSSL_PREFIX:INTERNAL=/usr
_OPENSSL_STATIC_CFLAGS:INTERNAL=
_OPENSSL_STATIC_CFLAGS_I:INTERNAL=
_OPENSSL_STATIC_CFLAGS_OTHER:INTERNAL=
_OPENSSL_STATIC_INCLUDE_DIRS:INTERNAL=
_OPENSSL_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lssl;-lcrypto;-ldl;-pthread
_OPENSSL_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
_OPENSSL_STATIC_LIBDIR:INTERNAL=
_OPENSSL_STATIC_LIBRARIES:INTERNAL=ssl;crypto;dl
_OPENSSL_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
_OPENSSL_STATIC_LIBS:INTERNAL=
_OPENSSL_STATIC_LIBS_L:INTERNAL=
_OPENSSL_STATIC_LIBS_OTHER:INTERNAL=
_OPENSSL_STATIC_LIBS_PATHS:INTERNAL=
_OPENSSL_VERSION:INTERNAL=3.0.2
_OPENSSL_openssl_INCLUDEDIR:INTERNAL=
_OPENSSL_openssl_LIBDIR:INTERNAL=
_OPENSSL_openssl_PREFIX:INTERNAL=
_OPENSSL_openssl_VERSION:INTERNAL=
_Python3_EXECUTABLE:INTERNAL=/usr/bin/python3
//Python3 Properties
_Python3_INTERPRETER_PROPERTIES:INTERNAL=Python;3;10;12;64;;cpython-310-x86_64-linux-gnu;/usr/lib/python3.10;/usr/lib/python3.10;/usr/lib/python3/dist-packages;/usr/lib/python3/dist-packages
_Python3_INTERPRETER_SIGNATURE:INTERNAL=0f3e53742e142b1d9e50e4ca5b901dd8
__pkg_config_arguments__OPENSSL:INTERNAL=QUIET;openssl
__pkg_config_checked__OPENSSL:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_crypto
pkgcfg_lib__OPENSSL_crypto-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_ssl
pkgcfg_lib__OPENSSL_ssl-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib/x86_64-linux-gnu

