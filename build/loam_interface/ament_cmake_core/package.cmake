set(_AMENT_PACKAGE_NAME "loam_interface")
set(loam_interface_VERSION "0.0.2")
set(loam_interface_MAINTAINER "Nina<PERSON> <<EMAIL>>")
set(loam_interface_BUILD_DEPENDS "rclcpp" "std_msgs" "nav_msgs" "geometry_msgs" "sensor_msgs" "tf2" "tf2_ros" "tf2_geometry_msgs" "message_filters" "pcl_ros" "pcl_conversions")
set(loam_interface_BUILDTOOL_DEPENDS "ament_cmake")
set(loam_interface_BUILD_EXPORT_DEPENDS "rclcpp" "std_msgs" "nav_msgs" "geometry_msgs" "sensor_msgs" "tf2" "tf2_ros" "tf2_geometry_msgs" "message_filters" "pcl_ros" "pcl_conversions")
set(loam_interface_BUILDTOOL_EXPORT_DEPENDS )
set(loam_interface_EXEC_DEPENDS "rclcpp" "std_msgs" "nav_msgs" "geometry_msgs" "sensor_msgs" "tf2" "tf2_ros" "tf2_geometry_msgs" "message_filters" "pcl_ros" "pcl_conversions")
set(loam_interface_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(loam_interface_GROUP_DEPENDS )
set(loam_interface_MEMBER_OF_GROUPS )
set(loam_interface_DEPRECATED "")
set(loam_interface_EXPORT_TAGS)
list(APPEND loam_interface_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
