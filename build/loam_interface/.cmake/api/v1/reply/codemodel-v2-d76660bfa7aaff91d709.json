{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-9b64faaf5faaf8ba840c.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "loam_interface", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "loamInterface::@6890427a1f51a3e7e1df", "jsonFile": "target-loamInterface-34f09828d42e33a90766.json", "name": "loamInterface", "projectIndex": 0}, {"directoryIndex": 0, "id": "loam_interface_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-loam_interface_uninstall-4e23b69959b8b811a5f1.json", "name": "loam_interface_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-c230472291d3534eb37d.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/saoxueche0808pm/saoxueche/build/loam_interface", "source": "/home/<USER>/saoxueche0808pm/saoxueche/src/loam_interface"}, "version": {"major": 2, "minor": 3}}