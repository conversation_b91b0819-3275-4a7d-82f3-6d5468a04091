set(_AMENT_PACKAGE_NAME "sensor_scan_generation")
set(sensor_scan_generation_VERSION "0.0.1")
set(sensor_scan_generation_MAINTAINER "<PERSON><PERSON><PERSON> <<EMAIL>>")
set(sensor_scan_generation_BUILD_DEPENDS "rclcpp" "std_msgs" "nav_msgs" "sensor_msgs" "pcl_ros" "tf2" "tf2_ros" "tf2_geometry_msgs" "message_filters" "pcl_conversions")
set(sensor_scan_generation_BUILDTOOL_DEPENDS "ament_cmake")
set(sensor_scan_generation_BUILD_EXPORT_DEPENDS "rclcpp" "std_msgs" "nav_msgs" "sensor_msgs" "pcl_ros" "tf2" "tf2_ros" "tf2_geometry_msgs" "message_filters" "pcl_conversions")
set(sensor_scan_generation_BUILDTOOL_EXPORT_DEPENDS )
set(sensor_scan_generation_EXEC_DEPENDS "rclcpp" "std_msgs" "nav_msgs" "sensor_msgs" "pcl_ros" "tf2" "tf2_ros" "tf2_geometry_msgs" "message_filters" "pcl_conversions")
set(sensor_scan_generation_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(sensor_scan_generation_GROUP_DEPENDS )
set(sensor_scan_generation_MEMBER_OF_GROUPS )
set(sensor_scan_generation_DEPRECATED "")
set(sensor_scan_generation_EXPORT_TAGS)
list(APPEND sensor_scan_generation_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
