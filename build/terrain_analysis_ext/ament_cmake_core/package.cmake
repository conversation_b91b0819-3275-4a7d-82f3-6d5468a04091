set(_AMENT_PACKAGE_NAME "terrain_analysis_ext")
set(terrain_analysis_ext_VERSION "0.0.1")
set(terrain_analysis_ext_MAINTAINER "<PERSON> <z<PERSON><PERSON>@cmu.edu>")
set(terrain_analysis_ext_BUILD_DEPENDS "rclcpp" "std_msgs" "sensor_msgs" "message_filters" "pcl_ros" "pcl_conversions" "nav_msgs" "geometry_msgs" "tf2" "tf2_ros" "tf2_geometry_msgs")
set(terrain_analysis_ext_BUILDTOOL_DEPENDS "ament_cmake")
set(terrain_analysis_ext_BUILD_EXPORT_DEPENDS "rclcpp" "std_msgs" "sensor_msgs" "message_filters" "pcl_ros" "pcl_conversions" "nav_msgs" "geometry_msgs" "tf2" "tf2_ros" "tf2_geometry_msgs")
set(terrain_analysis_ext_BUILDTOOL_EXPORT_DEPENDS )
set(terrain_analysis_ext_EXEC_DEPENDS "rclcpp" "std_msgs" "sensor_msgs" "message_filters" "pcl_ros" "pcl_conversions" "nav_msgs" "geometry_msgs" "tf2" "tf2_ros" "tf2_geometry_msgs")
set(terrain_analysis_ext_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(terrain_analysis_ext_GROUP_DEPENDS )
set(terrain_analysis_ext_MEMBER_OF_GROUPS )
set(terrain_analysis_ext_DEPRECATED "")
set(terrain_analysis_ext_EXPORT_TAGS)
list(APPEND terrain_analysis_ext_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
