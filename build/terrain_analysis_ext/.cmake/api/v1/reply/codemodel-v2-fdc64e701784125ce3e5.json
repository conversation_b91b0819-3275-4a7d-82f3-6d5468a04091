{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-1b3b2ca60211255c565f.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "terrain_analysis_ext", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "terrainAnalysisExt::@6890427a1f51a3e7e1df", "jsonFile": "target-terrainAnalysisExt-cc74305abdded0c9e0cf.json", "name": "terrainAnalysisExt", "projectIndex": 0}, {"directoryIndex": 0, "id": "terrain_analysis_ext_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-terrain_analysis_ext_uninstall-4121aa3e13437ed8f418.json", "name": "terrain_analysis_ext_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-7918c9ad693ae813ff63.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/saoxueche0808pm/saoxueche/build/terrain_analysis_ext", "source": "/home/<USER>/saoxueche0808pm/saoxueche/src/terrain_analysis_ext"}, "version": {"major": 2, "minor": 3}}