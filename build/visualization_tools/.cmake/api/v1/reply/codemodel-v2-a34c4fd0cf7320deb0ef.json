{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-9a08501714d5ab8ecc37.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "visualization_tools", "targetIndexes": [0, 1, 2, 3, 4]}], "targets": [{"directoryIndex": 0, "id": "ament_cmake_python_build_visualization_tools_egg::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_build_visualization_tools_egg-ebc6c1d15eae14c4fe72.json", "name": "ament_cmake_python_build_visualization_tools_egg", "projectIndex": 0}, {"directoryIndex": 0, "id": "ament_cmake_python_copy_visualization_tools::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_copy_visualization_tools-cef2af054ce40e76627f.json", "name": "ament_cmake_python_copy_visualization_tools", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-ebc99e48709e58ddf25c.json", "name": "uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "visualizationTools::@6890427a1f51a3e7e1df", "jsonFile": "target-visualizationTools-5b057bc5f7056dc5520a.json", "name": "visualizationTools", "projectIndex": 0}, {"directoryIndex": 0, "id": "visualization_tools_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-visualization_tools_uninstall-a70a4c06af57bc0aa783.json", "name": "visualization_tools_uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools", "source": "/home/<USER>/saoxueche0808pm/saoxueche/src/visualization_tools"}, "version": {"major": 2, "minor": 3}}