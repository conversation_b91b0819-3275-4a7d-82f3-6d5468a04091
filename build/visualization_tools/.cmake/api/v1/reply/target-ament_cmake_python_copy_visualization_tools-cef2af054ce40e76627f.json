{"backtrace": 3, "backtraceGraph": {"commands": ["add_custom_target", "_ament_cmake_python_install_package", "ament_python_install_package"], "files": ["/opt/ros/humble/share/ament_cmake_python/cmake/ament_python_install_package.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 2, "file": 1, "line": 46, "parent": 0}, {"command": 1, "file": 0, "line": 39, "parent": 1}, {"command": 0, "file": 0, "line": 122, "parent": 2}]}, "id": "ament_cmake_python_copy_visualization_tools::@6890427a1f51a3e7e1df", "name": "ament_cmake_python_copy_visualization_tools", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 3, "isGenerated": true, "path": "/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/CMakeFiles/ament_cmake_python_copy_visualization_tools", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/CMakeFiles/ament_cmake_python_copy_visualization_tools.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}