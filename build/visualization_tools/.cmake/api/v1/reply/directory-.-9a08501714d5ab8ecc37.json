{"backtraceGraph": {"commands": ["install", "ament_environment_hooks", "_ament_cmake_python_register_environment_hook", "ament_python_install_package", "_ament_cmake_python_install_package", "ament_index_register_resource", "ament_cmake_environment_generate_package_run_dependencies_marker", "include", "ament_execute_extensions", "ament_package", "ament_cmake_environment_generate_parent_prefix_path_marker", "ament_generate_package_environment", "ament_index_register_package", "_ament_package"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/ament_environment_hooks.cmake", "/opt/ros/humble/share/ament_cmake_python/cmake/ament_cmake_python-extras.cmake", "/opt/ros/humble/share/ament_cmake_python/cmake/ament_python_install_package.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/index/ament_index_register_resource.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/environment/ament_cmake_environment_package_hook.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/ament_cmake_environment_hooks_package_hook.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/ament_generate_package_environment.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/index/ament_index_register_package.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/index/ament_cmake_index_package_hook.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 36, "parent": 0}, {"command": 0, "file": 0, "line": 40, "parent": 0}, {"command": 3, "file": 0, "line": 46, "parent": 0}, {"command": 2, "file": 3, "line": 38, "parent": 3}, {"command": 1, "file": 2, "line": 36, "parent": 4}, {"command": 0, "file": 1, "line": 70, "parent": 5}, {"command": 0, "file": 1, "line": 87, "parent": 5}, {"command": 4, "file": 3, "line": 39, "parent": 3}, {"command": 0, "file": 3, "line": 154, "parent": 8}, {"command": 0, "file": 3, "line": 181, "parent": 8}, {"command": 0, "file": 3, "line": 191, "parent": 8}, {"command": 0, "file": 0, "line": 48, "parent": 0}, {"command": 9, "file": 0, "line": 58, "parent": 0}, {"command": 8, "file": 7, "line": 66, "parent": 13}, {"command": 7, "file": 6, "line": 48, "parent": 14}, {"file": 5, "parent": 15}, {"command": 6, "file": 5, "line": 47, "parent": 16}, {"command": 5, "file": 5, "line": 29, "parent": 17}, {"command": 0, "file": 4, "line": 105, "parent": 18}, {"command": 10, "file": 5, "line": 48, "parent": 16}, {"command": 5, "file": 5, "line": 43, "parent": 20}, {"command": 0, "file": 4, "line": 105, "parent": 21}, {"command": 7, "file": 6, "line": 48, "parent": 14}, {"file": 8, "parent": 23}, {"command": 1, "file": 8, "line": 20, "parent": 24}, {"command": 0, "file": 1, "line": 70, "parent": 25}, {"command": 0, "file": 1, "line": 87, "parent": 25}, {"command": 0, "file": 1, "line": 70, "parent": 25}, {"command": 0, "file": 1, "line": 87, "parent": 25}, {"command": 11, "file": 8, "line": 26, "parent": 24}, {"command": 0, "file": 9, "line": 91, "parent": 30}, {"command": 0, "file": 9, "line": 91, "parent": 30}, {"command": 0, "file": 9, "line": 91, "parent": 30}, {"command": 0, "file": 9, "line": 107, "parent": 30}, {"command": 0, "file": 9, "line": 119, "parent": 30}, {"command": 7, "file": 6, "line": 48, "parent": 14}, {"file": 11, "parent": 36}, {"command": 12, "file": 11, "line": 16, "parent": 37}, {"command": 5, "file": 10, "line": 29, "parent": 38}, {"command": 0, "file": 4, "line": 105, "parent": 39}, {"command": 13, "file": 7, "line": 68, "parent": 13}, {"command": 0, "file": 7, "line": 150, "parent": 41}, {"command": 0, "file": 7, "line": 157, "parent": 41}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib/visualization_tools", "paths": ["visualizationTools"], "targetId": "visualizationTools::@6890427a1f51a3e7e1df", "targetIndex": 3, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "destination": "share/visualization_tools", "paths": ["launch"], "type": "directory"}, {"backtrace": 6, "component": "Unspecified", "destination": "share/visualization_tools/environment", "paths": ["/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/ament_cmake_environment_hooks/pythonpath.sh"], "type": "file"}, {"backtrace": 7, "component": "Unspecified", "destination": "share/visualization_tools/environment", "paths": ["/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/ament_cmake_environment_hooks/pythonpath.dsv"], "type": "file"}, {"backtrace": 9, "component": "Unspecified", "destination": "local/lib/python3.10/dist-packages/visualization_tools-0.0.1-py3.10.egg-info", "paths": [{"from": "/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/ament_cmake_python/visualization_tools/visualization_tools.egg-info", "to": "."}], "type": "directory"}, {"backtrace": 10, "component": "Unspecified", "destination": "local/lib/python3.10/dist-packages/visualization_tools", "paths": [{"from": "visualization_tools", "to": "."}], "type": "directory"}, {"backtrace": 11, "component": "Unspecified", "type": "code"}, {"backtrace": 12, "component": "Unspecified", "destination": "lib/visualization_tools", "paths": ["scripts/realTimePlot.py"], "type": "file"}, {"backtrace": 19, "component": "Unspecified", "destination": "share/ament_index/resource_index/package_run_dependencies", "paths": ["/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/ament_cmake_index/share/ament_index/resource_index/package_run_dependencies/visualization_tools"], "type": "file"}, {"backtrace": 22, "component": "Unspecified", "destination": "share/ament_index/resource_index/parent_prefix_path", "paths": ["/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/ament_cmake_index/share/ament_index/resource_index/parent_prefix_path/visualization_tools"], "type": "file"}, {"backtrace": 26, "component": "Unspecified", "destination": "share/visualization_tools/environment", "paths": ["/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/environment/ament_prefix_path.sh"], "type": "file"}, {"backtrace": 27, "component": "Unspecified", "destination": "share/visualization_tools/environment", "paths": ["/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/ament_cmake_environment_hooks/ament_prefix_path.dsv"], "type": "file"}, {"backtrace": 28, "component": "Unspecified", "destination": "share/visualization_tools/environment", "paths": ["/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/environment/path.sh"], "type": "file"}, {"backtrace": 29, "component": "Unspecified", "destination": "share/visualization_tools/environment", "paths": ["/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/ament_cmake_environment_hooks/path.dsv"], "type": "file"}, {"backtrace": 31, "component": "Unspecified", "destination": "share/visualization_tools", "paths": ["/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/ament_cmake_environment_hooks/local_setup.bash"], "type": "file"}, {"backtrace": 32, "component": "Unspecified", "destination": "share/visualization_tools", "paths": ["/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/ament_cmake_environment_hooks/local_setup.sh"], "type": "file"}, {"backtrace": 33, "component": "Unspecified", "destination": "share/visualization_tools", "paths": ["/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/ament_cmake_environment_hooks/local_setup.zsh"], "type": "file"}, {"backtrace": 34, "component": "Unspecified", "destination": "share/visualization_tools", "paths": ["/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/ament_cmake_environment_hooks/local_setup.dsv"], "type": "file"}, {"backtrace": 35, "component": "Unspecified", "destination": "share/visualization_tools", "paths": ["/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/ament_cmake_environment_hooks/package.dsv"], "type": "file"}, {"backtrace": 40, "component": "Unspecified", "destination": "share/ament_index/resource_index/packages", "paths": ["/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/ament_cmake_index/share/ament_index/resource_index/packages/visualization_tools"], "type": "file"}, {"backtrace": 42, "component": "Unspecified", "destination": "share/visualization_tools/cmake", "paths": ["/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/ament_cmake_core/visualization_toolsConfig.cmake", "/home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/ament_cmake_core/visualization_toolsConfig-version.cmake"], "type": "file"}, {"backtrace": 43, "component": "Unspecified", "destination": "share/visualization_tools", "paths": ["package.xml"], "type": "file"}], "paths": {"build": ".", "source": "."}}