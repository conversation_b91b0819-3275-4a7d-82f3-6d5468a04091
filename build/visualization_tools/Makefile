# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/saoxueche0808pm/saoxueche/src/visualization_tools

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/CMakeFiles /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/saoxueche0808pm/saoxueche/build/visualization_tools/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named uninstall

# Build rule for target.
uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 uninstall
.PHONY : uninstall

# fast build rule for target.
uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/build
.PHONY : uninstall/fast

#=============================================================================
# Target rules for targets named visualization_tools_uninstall

# Build rule for target.
visualization_tools_uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 visualization_tools_uninstall
.PHONY : visualization_tools_uninstall

# fast build rule for target.
visualization_tools_uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/visualization_tools_uninstall.dir/build.make CMakeFiles/visualization_tools_uninstall.dir/build
.PHONY : visualization_tools_uninstall/fast

#=============================================================================
# Target rules for targets named visualizationTools

# Build rule for target.
visualizationTools: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 visualizationTools
.PHONY : visualizationTools

# fast build rule for target.
visualizationTools/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/visualizationTools.dir/build.make CMakeFiles/visualizationTools.dir/build
.PHONY : visualizationTools/fast

#=============================================================================
# Target rules for targets named ament_cmake_python_copy_visualization_tools

# Build rule for target.
ament_cmake_python_copy_visualization_tools: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ament_cmake_python_copy_visualization_tools
.PHONY : ament_cmake_python_copy_visualization_tools

# fast build rule for target.
ament_cmake_python_copy_visualization_tools/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ament_cmake_python_copy_visualization_tools.dir/build.make CMakeFiles/ament_cmake_python_copy_visualization_tools.dir/build
.PHONY : ament_cmake_python_copy_visualization_tools/fast

#=============================================================================
# Target rules for targets named ament_cmake_python_build_visualization_tools_egg

# Build rule for target.
ament_cmake_python_build_visualization_tools_egg: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ament_cmake_python_build_visualization_tools_egg
.PHONY : ament_cmake_python_build_visualization_tools_egg

# fast build rule for target.
ament_cmake_python_build_visualization_tools_egg/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ament_cmake_python_build_visualization_tools_egg.dir/build.make CMakeFiles/ament_cmake_python_build_visualization_tools_egg.dir/build
.PHONY : ament_cmake_python_build_visualization_tools_egg/fast

src/visualizationTools.o: src/visualizationTools.cpp.o
.PHONY : src/visualizationTools.o

# target to build an object file
src/visualizationTools.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/visualizationTools.dir/build.make CMakeFiles/visualizationTools.dir/src/visualizationTools.cpp.o
.PHONY : src/visualizationTools.cpp.o

src/visualizationTools.i: src/visualizationTools.cpp.i
.PHONY : src/visualizationTools.i

# target to preprocess a source file
src/visualizationTools.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/visualizationTools.dir/build.make CMakeFiles/visualizationTools.dir/src/visualizationTools.cpp.i
.PHONY : src/visualizationTools.cpp.i

src/visualizationTools.s: src/visualizationTools.cpp.s
.PHONY : src/visualizationTools.s

# target to generate assembly for a file
src/visualizationTools.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/visualizationTools.dir/build.make CMakeFiles/visualizationTools.dir/src/visualizationTools.cpp.s
.PHONY : src/visualizationTools.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... ament_cmake_python_build_visualization_tools_egg"
	@echo "... ament_cmake_python_copy_visualization_tools"
	@echo "... uninstall"
	@echo "... visualization_tools_uninstall"
	@echo "... visualizationTools"
	@echo "... src/visualizationTools.o"
	@echo "... src/visualizationTools.i"
	@echo "... src/visualizationTools.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

