set(_AMENT_PACKAGE_NAME "visualization_tools")
set(visualization_tools_VERSION "0.0.1")
set(visualization_tools_MAINTAINER "<PERSON><PERSON><PERSON> <<EMAIL>>")
set(visualization_tools_BUILD_DEPENDS "rclcpp" "rclpy" "std_msgs" "sensor_msgs" "message_filters" "pcl_ros" "pcl_conversions" "nav_msgs" "geometry_msgs" "tf2" "tf2_ros" "tf2_geometry_msgs")
set(visualization_tools_BUILDTOOL_DEPENDS "ament_cmake" "ament_cmake_python")
set(visualization_tools_BUILD_EXPORT_DEPENDS "rclcpp" "rclpy" "std_msgs" "sensor_msgs" "message_filters" "pcl_ros" "pcl_conversions" "nav_msgs" "geometry_msgs" "tf2" "tf2_ros" "tf2_geometry_msgs")
set(visualization_tools_BUILDTOOL_EXPORT_DEPENDS )
set(visualization_tools_EXEC_DEPENDS "rclcpp" "rclpy" "std_msgs" "sensor_msgs" "message_filters" "pcl_ros" "pcl_conversions" "nav_msgs" "geometry_msgs" "tf2" "tf2_ros" "tf2_geometry_msgs")
set(visualization_tools_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(visualization_tools_GROUP_DEPENDS )
set(visualization_tools_MEMBER_OF_GROUPS )
set(visualization_tools_DEPRECATED "")
set(visualization_tools_EXPORT_TAGS)
list(APPEND visualization_tools_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
