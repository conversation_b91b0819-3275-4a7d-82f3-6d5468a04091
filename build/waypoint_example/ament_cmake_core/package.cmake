set(_AMENT_PACKAGE_NAME "waypoint_example")
set(waypoint_example_VERSION "0.0.1")
set(waypoint_example_MAINTAINER "<PERSON> <PERSON> <z<PERSON><PERSON>@cmu.edu>")
set(waypoint_example_BUILD_DEPENDS "rclcpp" "std_msgs" "sensor_msgs" "message_filters" "pcl_ros" "pcl_conversions" "nav_msgs" "geometry_msgs" "tf2" "tf2_ros" "tf2_geometry_msgs")
set(waypoint_example_BUILDTOOL_DEPENDS "ament_cmake")
set(waypoint_example_BUILD_EXPORT_DEPENDS "rclcpp" "std_msgs" "sensor_msgs" "message_filters" "pcl_ros" "pcl_conversions" "nav_msgs" "geometry_msgs" "tf2" "tf2_ros" "tf2_geometry_msgs")
set(waypoint_example_BUILDTOOL_EXPORT_DEPENDS )
set(waypoint_example_EXEC_DEPENDS "rclcpp" "std_msgs" "sensor_msgs" "message_filters" "pcl_ros" "pcl_conversions" "nav_msgs" "geometry_msgs" "tf2" "tf2_ros" "tf2_geometry_msgs")
set(waypoint_example_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(waypoint_example_GROUP_DEPENDS )
set(waypoint_example_MEMBER_OF_GROUPS )
set(waypoint_example_DEPRECATED "")
set(waypoint_example_EXPORT_TAGS)
list(APPEND waypoint_example_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
