{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-c68a203f5584f294f749.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "waypoint_example", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-04671717b9e2491231e7.json", "name": "uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "waypointExample::@6890427a1f51a3e7e1df", "jsonFile": "target-waypointExample-7fc337ed3080fdcb2648.json", "name": "waypointExample", "projectIndex": 0}, {"directoryIndex": 0, "id": "waypoint_example_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-waypoint_example_uninstall-b70e6110d7309e944ed4.json", "name": "waypoint_example_uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_example", "source": "/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_example"}, "version": {"major": 2, "minor": 3}}