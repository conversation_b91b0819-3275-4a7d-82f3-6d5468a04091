set(_AMENT_PACKAGE_NAME "local_planner")
set(local_planner_VERSION "0.0.1")
set(local_planner_MAINTAINER "<PERSON> <PERSON> <zhang<PERSON>@cmu.edu>")
set(local_planner_BUILD_DEPENDS "rclcpp" "std_msgs" "nav_msgs" "sensor_msgs" "geometry_msgs" "pcl_ros" "tf2" "tf2_ros" "tf2_geometry_msgs" "message_filters" "pcl_conversions")
set(local_planner_BUILDTOOL_DEPENDS "ament_cmake")
set(local_planner_BUILD_EXPORT_DEPENDS "rclcpp" "std_msgs" "nav_msgs" "sensor_msgs" "geometry_msgs" "pcl_ros" "tf2" "tf2_ros" "tf2_geometry_msgs" "message_filters" "pcl_conversions")
set(local_planner_BUILDTOOL_EXPORT_DEPENDS )
set(local_planner_EXEC_DEPENDS "rclcpp" "std_msgs" "nav_msgs" "sensor_msgs" "geometry_msgs" "pcl_ros" "tf2" "tf2_ros" "tf2_geometry_msgs" "message_filters" "pcl_conversions")
set(local_planner_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(local_planner_GROUP_DEPENDS )
set(local_planner_MEMBER_OF_GROUPS )
set(local_planner_DEPRECATED "")
set(local_planner_EXPORT_TAGS)
list(APPEND local_planner_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
