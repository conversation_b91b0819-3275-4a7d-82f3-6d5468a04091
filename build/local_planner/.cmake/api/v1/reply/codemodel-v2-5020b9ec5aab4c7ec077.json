{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-9b0290de374c167ce07e.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "local_planner", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "localPlanner::@6890427a1f51a3e7e1df", "jsonFile": "target-localPlanner-59de64ff9e5d0543525e.json", "name": "localPlanner", "projectIndex": 0}, {"directoryIndex": 0, "id": "local_planner_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-local_planner_uninstall-fd2ef6907f7a7a48c098.json", "name": "local_planner_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "pathFollower::@6890427a1f51a3e7e1df", "jsonFile": "target-pathFollower-965f2f358fd0aa5e7ebf.json", "name": "pathFollower", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-d9e98fced035fe15bcaa.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/saoxueche0808pm/saoxueche/build/local_planner", "source": "/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner"}, "version": {"major": 2, "minor": 3}}