{"artifacts": [{"path": "localPlanner"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "ament_target_dependencies", "add_compile_options", "target_compile_definitions", "include_directories", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 34, "parent": 0}, {"command": 1, "file": 0, "line": 39, "parent": 0}, {"command": 3, "file": 0, "line": 36, "parent": 0}, {"command": 2, "file": 1, "line": 145, "parent": 3}, {"command": 2, "file": 1, "line": 151, "parent": 3}, {"command": 4, "file": 0, "line": 15, "parent": 0}, {"command": 5, "file": 1, "line": 128, "parent": 3}, {"command": 6, "file": 0, "line": 32, "parent": 0}, {"command": 7, "file": 1, "line": 141, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"backtrace": 6, "fragment": "-Wall"}, {"backtrace": 6, "fragment": "-Wextra"}, {"backtrace": 6, "fragment": "-Wpedantic"}, {"backtrace": 4, "fragment": "-fPIC"}], "defines": [{"backtrace": 4, "define": "BOOST_ALL_NO_LIB"}, {"backtrace": 4, "define": "BOOST_DATE_TIME_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_FILESYSTEM_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_IOSTREAMS_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_SERIALIZATION_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_SYSTEM_DYN_LINK"}, {"backtrace": 4, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 7, "define": "DISABLE_PCAP"}, {"backtrace": 4, "define": "QT_CORE_LIB"}, {"backtrace": 4, "define": "QT_GUI_LIB"}, {"backtrace": 4, "define": "QT_NO_DEBUG"}, {"backtrace": 4, "define": "QT_OPENGL_LIB"}, {"backtrace": 4, "define": "QT_WIDGETS_LIB"}, {"backtrace": 4, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"backtrace": 4, "define": "kiss_fft_scalar=double"}], "includes": [{"backtrace": 8, "path": "/home/<USER>/saoxueche0808pm/saoxueche/src/local_planner/include"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/nav_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/tf2"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/tf2_ros"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/tf2_geometry_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/message_filters"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/pcl_ros"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/pcl_conversions"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/pcl_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/usr/include/pcl-1.12"}, {"backtrace": 9, "isSystem": true, "path": "/usr/include/eigen3"}, {"backtrace": 9, "isSystem": true, "path": "/usr/include/ni"}, {"backtrace": 9, "isSystem": true, "path": "/usr/include/openni2"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_action"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_action"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/vtk-9.1"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/jsoncpp"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/freetype2"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5/QtOpenGL"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5/QtWidgets"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5/QtGui"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5/QtCore"}, {"backtrace": 4, "isSystem": true, "path": "/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_components"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/class_loader"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/composition_interfaces"}], "language": "CXX", "languageStandard": {"backtraces": [4, 4, 4, 4], "standard": "17"}, "sourceIndexes": [0]}], "id": "localPlanner::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib/local_planner"}], "prefix": {"path": "/home/<USER>/saoxueche0808pm/saoxueche/install/local_planner"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_ros_tf.a", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcd_to_pointcloud_lib.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libmessage_filters.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 5, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_apps.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_outofcore.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_people.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/libOpenNI.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libOpenNI2.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libusb-1.0.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libflann_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatic_transform_broadcaster_node.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libtf2_ros.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libtf2.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libmessage_filters.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librclcpp_action.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_action.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/liborocos-kdl.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libpcl_common.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "-llz4", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libqhull_r.so.8.0.2", "role": "libraries"}, {"fragment": "-lm", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libmessage_filters.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomponent_manager.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libclass_loader.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_surface.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_keypoints.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_tracking.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_recognition.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_registration.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_stereo.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_segmentation.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_features.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_filters.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_ml.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_visualization.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_search.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_kdtree.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_io.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_octree.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libpng.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/libOpenNI.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libOpenNI2.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libusb-1.0.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkChartsCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkInteractionImage-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkIOGeometry-9.1.so.9.1.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libjsoncpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkIOPLY-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkRenderingLOD-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkViewsContext2D-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkViewsCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkGUISupportQt-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkInteractionWidgets-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkFiltersModeling-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkInteractionStyle-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkFiltersExtraction-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkIOLegacy-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkIOCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkRenderingAnnotation-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkRenderingContext2D-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkRenderingFreeType-9.1.so.9.1.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libfreetype.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkImagingSources-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkIOImage-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkImagingCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkRenderingOpenGL2-9.1.so.9.1.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libvtkRenderingUI-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkRenderingCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonColor-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkFiltersGeometry-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkFiltersSources-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkFiltersGeneral-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonComputationalGeometry-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkFiltersCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonExecutionModel-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonDataModel-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonMisc-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonTransforms-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonMath-9.1.so.9.1.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libvtkkissfft-9.1.so.9.1.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libGLEW.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libX11.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libQt5OpenGL.so.5.15.3", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libQt5Widgets.so.5.15.3", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libQt5Gui.so.5.15.3", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libQt5Core.so.5.15.3", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonCore-9.1.so.9.1.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libtbb.so.12.5", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libvtksys-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 5, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_common.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libboost_system.so.1.74.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.74.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.74.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libboost_iostreams.so.1.74.0", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/x86_64-linux-gnu/libboost_serialization.so.1.74.0", "role": "libraries"}], "language": "CXX"}, "name": "localPlanner", "nameOnDisk": "localPlanner", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/localPlanner.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}