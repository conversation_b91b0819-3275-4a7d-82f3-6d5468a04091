set(_AMENT_PACKAGE_NAME "waypoint_rviz_plugin")
set(waypoint_rviz_plugin_VERSION "0.0.1")
set(waypoint_rviz_plugin_MAINTAINER "<PERSON> <PERSON> <<EMAIL>>")
set(waypoint_rviz_plugin_BUILD_DEPENDS "rclcpp" "rclpy" "std_msgs" "rviz2" "rviz_common" "rviz_rendering" "rviz_default_plugins" "qtbase5-dev" "geometry_msgs" "libqt5-core" "libqt5-gui" "libqt5-widgets" "pluginlib")
set(waypoint_rviz_plugin_BUILDTOOL_DEPENDS "ament_cmake" "ament_cmake_python")
set(waypoint_rviz_plugin_BUILD_EXPORT_DEPENDS "rclcpp" "rclpy" "std_msgs" "rviz2" "rviz_common" "rviz_rendering" "rviz_default_plugins" "qtbase5-dev" "geometry_msgs" "libqt5-core" "libqt5-gui" "libqt5-widgets" "pluginlib")
set(waypoint_rviz_plugin_BUILDTOOL_EXPORT_DEPENDS )
set(waypoint_rviz_plugin_EXEC_DEPENDS "rclcpp" "rclpy" "std_msgs" "rviz2" "rviz_common" "rviz_rendering" "rviz_default_plugins" "qtbase5-dev" "geometry_msgs" "libqt5-core" "libqt5-gui" "libqt5-widgets" "pluginlib")
set(waypoint_rviz_plugin_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(waypoint_rviz_plugin_GROUP_DEPENDS )
set(waypoint_rviz_plugin_MEMBER_OF_GROUPS )
set(waypoint_rviz_plugin_DEPRECATED "")
set(waypoint_rviz_plugin_EXPORT_TAGS)
list(APPEND waypoint_rviz_plugin_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
