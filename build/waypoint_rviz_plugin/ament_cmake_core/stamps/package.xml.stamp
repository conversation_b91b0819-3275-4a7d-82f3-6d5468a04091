<package format="3">
  <name>waypoint_rviz_plugin</name>
  <version>0.0.1</version>
  <description>Waypoint RVIZ Plugin</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>BSD</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>ament_cmake_python</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>rclpy</depend>
  <depend>std_msgs</depend>
  <depend>rviz2</depend>
  <depend>rviz_common</depend>
  <depend>rviz_rendering</depend>
  <depend>rviz_default_plugins</depend>
  <depend>qtbase5-dev</depend>
  <depend>geometry_msgs</depend>
  <depend>libqt5-core</depend>
  <depend>libqt5-gui</depend>
  <depend>libqt5-widgets</depend>
  <depend>pluginlib</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
