# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DDEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DRCUTILS_ENABLE_FAULT_INJECTION -Dwaypoint_rviz_plugin_EXPORTS

CXX_INCLUDES = -I/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin/waypoint_rviz_plugin_autogen/include -I/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_rviz_plugin/include -isystem /opt/ros/humble/include/geometry_msgs -isystem /opt/ros/humble/include/rclcpp -isystem /opt/ros/humble/include/rviz_common -isystem /opt/ros/humble/include/rviz_rendering -isystem /opt/ros/humble/include/sensor_msgs -isystem /opt/ros/humble/include/rviz_default_plugins -isystem /opt/ros/humble/include/std_msgs -isystem /usr/include/eigen3 -isystem /usr/include/x86_64-linux-gnu/qt5 -isystem /usr/include/x86_64-linux-gnu/qt5/QtWidgets -isystem /usr/include/x86_64-linux-gnu/qt5/QtGui -isystem /usr/include/x86_64-linux-gnu/qt5/QtCore -isystem /usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -isystem /opt/ros/humble/include/builtin_interfaces -isystem /opt/ros/humble/include/rosidl_runtime_c -isystem /opt/ros/humble/include/rcutils -isystem /opt/ros/humble/include/rosidl_typesupport_interface -isystem /opt/ros/humble/include/fastcdr -isystem /opt/ros/humble/include/rosidl_runtime_cpp -isystem /opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp -isystem /opt/ros/humble/include/rmw -isystem /opt/ros/humble/include/rosidl_typesupport_fastrtps_c -isystem /opt/ros/humble/include/rosidl_typesupport_introspection_c -isystem /opt/ros/humble/include/rosidl_typesupport_introspection_cpp -isystem /opt/ros/humble/include/ament_index_cpp -isystem /opt/ros/humble/include/libstatistics_collector -isystem /opt/ros/humble/include/rcl -isystem /opt/ros/humble/include/rcl_interfaces -isystem /opt/ros/humble/include/rcl_logging_interface -isystem /opt/ros/humble/include/rcl_yaml_param_parser -isystem /opt/ros/humble/include/libyaml_vendor -isystem /opt/ros/humble/include/tracetools -isystem /opt/ros/humble/include/rcpputils -isystem /opt/ros/humble/include/statistics_msgs -isystem /opt/ros/humble/include/rosgraph_msgs -isystem /opt/ros/humble/include/rosidl_typesupport_cpp -isystem /opt/ros/humble/include/rosidl_typesupport_c -isystem /opt/ros/humble/opt/rviz_ogre_vendor/include/OGRE -isystem /opt/ros/humble/include/pluginlib -isystem /opt/ros/humble/include/class_loader -isystem /opt/ros/humble/include/resource_retriever -isystem /opt/ros/humble/include/tf2 -isystem /opt/ros/humble/include/tf2_geometry_msgs -isystem /opt/ros/humble/include/tf2_ros -isystem /opt/ros/humble/include/message_filters -isystem /opt/ros/humble/include/rclcpp_action -isystem /opt/ros/humble/include/action_msgs -isystem /opt/ros/humble/include/unique_identifier_msgs -isystem /opt/ros/humble/include/rcl_action -isystem /opt/ros/humble/include/tf2_msgs -isystem /opt/ros/humble/include/urdf -isystem /opt/ros/humble/include/urdf_parser_plugin -isystem /opt/ros/humble/include/urdfdom_headers -isystem /opt/ros/humble/include/urdfdom -isystem /opt/ros/humble/include/image_transport -isystem /opt/ros/humble/include/interactive_markers -isystem /opt/ros/humble/include/visualization_msgs -isystem /opt/ros/humble/include/laser_geometry -isystem /opt/ros/humble/include -isystem /opt/ros/humble/include/map_msgs -isystem /opt/ros/humble/include/nav_msgs

CXX_FLAGS = -fPIC -Wall -Wextra -Wpedantic -fPIC

