{"artifacts": [{"path": "libwaypoint_rviz_plugin.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "ament_target_dependencies", "add_compile_options", "include_directories", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 49, "parent": 0}, {"command": 1, "file": 0, "line": 90, "parent": 0}, {"command": 3, "file": 0, "line": 51, "parent": 0}, {"command": 2, "file": 1, "line": 145, "parent": 3}, {"command": 4, "file": 0, "line": 10, "parent": 0}, {"command": 5, "file": 0, "line": 47, "parent": 0}, {"command": 6, "file": 1, "line": 141, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 5, "fragment": "-Wall"}, {"backtrace": 5, "fragment": "-Wextra"}, {"backtrace": 5, "fragment": "-Wpedantic"}, {"backtrace": 4, "fragment": "-fPIC"}], "defines": [{"backtrace": 4, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 4, "define": "QT_CORE_LIB"}, {"backtrace": 4, "define": "QT_GUI_LIB"}, {"backtrace": 4, "define": "QT_NO_DEBUG"}, {"backtrace": 4, "define": "QT_WIDGETS_LIB"}, {"backtrace": 4, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "waypoint_rviz_plugin_EXPORTS"}], "includes": [{"backtrace": 0, "path": "/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin/waypoint_rviz_plugin_autogen/include"}, {"backtrace": 6, "path": "/home/<USER>/saoxueche0808pm/saoxueche/src/waypoint_rviz_plugin/include"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rviz_common"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rviz_rendering"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rviz_default_plugins"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/usr/include/eigen3"}, {"backtrace": 7, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5"}, {"backtrace": 7, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5/QtWidgets"}, {"backtrace": 7, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5/QtGui"}, {"backtrace": 7, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5/QtCore"}, {"backtrace": 7, "isSystem": true, "path": "/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/opt/rviz_ogre_vendor/include/OGRE"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/pluginlib"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/class_loader"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/resource_retriever"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2_geometry_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2_ros"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/message_filters"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_action"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_action"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/urdf"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/urdf_parser_plugin"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/urdfdom_headers"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/urdfdom"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/image_transport"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/interactive_markers"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/visualization_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/laser_geometry"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/map_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/nav_msgs"}], "language": "CXX", "languageStandard": {"backtraces": [4, 4, 4], "standard": "17"}, "sourceIndexes": [0, 1, 3]}], "dependencies": [{"backtrace": 0, "id": "waypoint_rviz_plugin_autogen::@6890427a1f51a3e7e1df"}], "id": "waypoint_rviz_plugin::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib/waypoint_rviz_plugin"}, {"backtrace": 2, "path": "lib/waypoint_rviz_plugin"}], "prefix": {"path": "/home/<USER>/saoxueche0808pm/saoxueche/install/waypoint_rviz_plugin"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librviz_default_plugins.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librviz_common.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libyaml-cpp.so.0.7.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librviz_rendering.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libQt5Widgets.so.5.15.3", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libQt5Gui.so.5.15.3", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libQt5Core.so.5.15.3", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libassimp.so.5.2.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libdraco.so.4.0.0", "role": "libraries"}, {"fragment": "-l<PERSON>zip", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/librt.a", "role": "libraries"}, {"fragment": "/opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreOverlay.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/opt/rviz_ogre_vendor/lib/libOgreMain.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libfreeimage.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libfreetype.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libOpenGL.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libGLX.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libGLU.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libSM.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libICE.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libX11.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libXext.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libXt.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libXrandr.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libXaw.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libresource_retriever.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libcurl.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/liborocos-kdl.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatic_transform_broadcaster_node.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_ros.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librclcpp_action.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_action.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/liburdf.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libclass_loader.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libtinyxml2.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/x86_64-linux-gnu/liburdfdom_sensor.so.3.0", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/x86_64-linux-gnu/liburdfdom_model_state.so.3.0", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/x86_64-linux-gnu/liburdfdom_model.so.3.0", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/x86_64-linux-gnu/liburdfdom_world.so.3.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libtinyxml.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/x86_64-linux-gnu/libimage_transport.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libmessage_filters.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libinteractive_markers.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/liblaser_geometry.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libmap_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}], "language": "CXX"}, "name": "waypoint_rviz_plugin", "nameOnDisk": "libwaypoint_rviz_plugin.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 3]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2]}, {"name": "CMake Rules", "sourceIndexes": [4]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin/waypoint_rviz_plugin_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/waypoint_tool.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "include/waypoint_tool.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin/include/moc_waypoint_tool.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/saoxueche0808pm/saoxueche/build/waypoint_rviz_plugin/include/moc_waypoint_tool.cpp.rule", "sourceGroupIndex": 2}], "type": "SHARED_LIBRARY"}