{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "Bash(rosdep install:*)", "Bash(colcon build:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(source:*)", "Bash(ros2 launch:*)", "Bash(timeout 10s ros2 node list)", "Bash(ros2 pkg list:*)", "Bash(ros2 plugin list:*)", "Bash(ros2 pkg executables:*)", "<PERSON><PERSON>(ros2 run:*)", "Bash(ros2 node:*)", "Bash(ros2 topic list:*)", "Bash(ros2 service:*)", "Bash(timeout 30s ros2 launch:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(ros2 action list)", "mcp__thinking__sequentialthinking", "<PERSON><PERSON>(ros2 topic echo:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm /home/<USER>/saoxueche/src/local_planner/src/pure_pursuit_controller.cpp)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(chmod:*)", "Bash(rm:*)", "Bash(claude mcp add thinking -s user -- npx -y @modelcontextprotocol/server-sequential-thinking)", "Bash(ros2 lifecycle set:*)", "Bash(ros2 topic info /map)", "Bash(ros2 lifecycle get:*)", "Bash(cat /home/<USER>/saoxueche/install/integrated_navigation/share/integrated_navigation/config/integrated_nav_params.yaml)", "Bash(grep -n \"visualizationTools\" /home/<USER>/saoxueche/src/integrated_navigation/launch/algorithm_only.launch.py)", "<PERSON><PERSON>(kill -TERM 49745 49746 49762 49764)", "Bash(ros2 param get:*)", "Bash(ros2 topic info:*)", "<PERSON><PERSON>(ros2 param list:*)", "Bash(ros2 log get-level:*)", "Bash(ros2 topic hz /cmd_vel)"], "deny": []}}