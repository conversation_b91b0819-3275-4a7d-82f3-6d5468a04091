#!/usr/bin/env python

"""
    ROS1 Keyboard Control for cmd_vel
    Modified from original ROS2 version
    Author: <PERSON><PERSON><PERSON><PERSON>
    Email: <EMAIL>
    Ref: https://stackoverflow.com/questions/510357/how-to-read-a-single-character-from-the-user
"""

import rospy
from geometry_msgs.msg import Twist


class _Getch:
    """
    Gets a single character from standard input. 
    Does not echo to the screen.
    """

    def __init__(self):
        try:
            self.impl = _GetchWindows()
        except ImportError:
            self.impl = _GetchUnix()

    def __call__(self):
        return self.impl()


class _GetchUnix:
    def __init__(self):
        import tty
        import sys

    def __call__(self):
        import sys
        import tty
        import termios
        fd = sys.stdin.fileno()
        old_settings = termios.tcgetattr(fd)
        try:
            tty.setraw(sys.stdin.fileno())
            ch = sys.stdin.read(1)
        finally:
            termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)
        return ch


class _GetchWindows:
    def __init__(self):
        import msvcrt

    def __call__(self):
        import msvcrt
        return msvcrt.getch()


class TeleopPublisher:

    def __init__(self):
        rospy.init_node('bcr_teleop_node', anonymous=True)
        self.vel_publisher = rospy.Publisher('/cmd_vel', Twist, queue_size=1)
        self.cmd_vel_msg = Twist()

        # 创建定时器，以固定频率发布速度命令
        timer_period = 0.05  # 20Hz
        self.timer = rospy.Timer(rospy.Duration(timer_period), self.velocity_publish_event)

    def set_vel(self, v, w):
        # 限制速度范围，防止过大的速度值
        max_linear_vel = 2.0   # 最大线速度 m/s
        max_angular_vel = 2.0  # 最大角速度 rad/s

        v = max(-max_linear_vel, min(max_linear_vel, v))
        w = max(-max_angular_vel, min(max_angular_vel, w))

        rospy.loginfo("Linear: %.2f m/s, Angular: %.2f rad/s" % (v, w))
        self.cmd_vel_msg.linear.x = v
        self.cmd_vel_msg.linear.y = 0.0
        self.cmd_vel_msg.linear.z = 0.0
        self.cmd_vel_msg.angular.x = 0.0
        self.cmd_vel_msg.angular.y = 0.0
        self.cmd_vel_msg.angular.z = w

    def velocity_publish_event(self, event):
        """定时器回调函数，定期发布速度命令"""
        self.vel_publisher.publish(self.cmd_vel_msg)


def main():
    try:
        getch = _Getch()
        publish_node = TeleopPublisher()

        v = 0.0
        w = 0.0

        print("\n" + "="*50)
        print("ROS1 Keyboard Control for cmd_vel")
        print("="*50)
        print("Controls:")
        print("  w/s : increase/decrease linear velocity")
        print("  a/d : increase/decrease angular velocity")
        print("  x   : stop (zero velocity)")
        print("  q   : quit")
        print("  r   : reset velocities to zero")
        print("\nCurrent velocity will be displayed after each command.")
        print("Press any key to start...")
        print("="*50)

        vel_step = 0.1  # 速度增量步长

        while not rospy.is_shutdown():
            key_in = getch()

            if key_in == "w":
                v += vel_step
                print("Forward: Linear velocity increased")
            elif key_in == "s":
                v -= vel_step
                print("Backward: Linear velocity decreased")
            elif key_in == "a":
                w += vel_step
                print("Turn Left: Angular velocity increased")
            elif key_in == "d":
                w -= vel_step
                print("Turn Right: Angular velocity decreased")
            elif key_in == "x" or key_in == " ":
                v = 0.0
                w = 0.0
                print("STOP: All velocities set to zero")
            elif key_in == "r":
                v = 0.0
                w = 0.0
                print("RESET: Velocities reset to zero")
            elif key_in == "q":
                print("Quitting...")
                break
            else:
                print("Invalid key! Use w/s/a/d/x/r/q")
                continue

            publish_node.set_vel(v, w)

    except KeyboardInterrupt:
        rospy.loginfo("Keyboard interrupt received, shutting down...")
    except rospy.ROSInterruptException:
        pass
    finally:
        # 发送零速度命令停止机器人
        publish_node.set_vel(0.0, 0.0)
        rospy.sleep(0.1)  # 确保最后的命令被发送


if __name__ == "__main__":
    main()
