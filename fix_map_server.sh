#!/bin/bash

# 地图服务器修复脚本
echo "=== 地图服务器诊断和修复 ==="

# 检查ROS2环境
if ! command -v ros2 &> /dev/null; then
    echo "❌ ROS2未找到，请先source环境："
    echo "source /opt/ros/humble/setup.bash"
    echo "source install/setup.bash"
    exit 1
fi

echo "✅ ROS2环境已加载"

# 检查地图文件是否存在
MAP_FILE="src/integrated_navigation/maps/test_map.yaml"
if [ ! -f "$MAP_FILE" ]; then
    echo "❌ 地图文件不存在: $MAP_FILE"
    exit 1
fi

echo "✅ 地图文件存在: $MAP_FILE"

# 检查地图服务器节点
if ros2 node list | grep -q "map_server"; then
    echo "✅ map_server节点正在运行"
    
    # 检查生命周期状态
    LIFECYCLE_STATE=$(ros2 lifecycle get /map_server 2>/dev/null)
    echo "📊 地图服务器状态: $LIFECYCLE_STATE"
    
    if [ "$LIFECYCLE_STATE" != "active [3]" ]; then
        echo "🔧 激活地图服务器..."
        
        # 配置地图服务器
        if ros2 lifecycle set /map_server configure 2>/dev/null; then
            echo "✅ 地图服务器已配置"
        else
            echo "❌ 地图服务器配置失败"
        fi
        
        # 激活地图服务器
        if ros2 lifecycle set /map_server activate 2>/dev/null; then
            echo "✅ 地图服务器已激活"
        else
            echo "❌ 地图服务器激活失败"
        fi
    else
        echo "✅ 地图服务器已处于活动状态"
    fi
    
    # 测试地图数据发布
    echo "🔍 测试地图数据发布..."
    if timeout 5s ros2 topic echo /map --once > /dev/null 2>&1; then
        echo "✅ 地图数据正常发布"
        
        # 显示地图信息
        echo "📋 地图信息:"
        ros2 topic echo /map --once | head -20
    else
        echo "❌ 地图数据未发布，请检查配置"
    fi
    
else
    echo "❌ map_server节点未运行"
    echo "请先启动导航系统:"
    echo "ros2 launch integrated_navigation algorithm_only.launch.py"
fi

echo "=== 诊断完成 ==="