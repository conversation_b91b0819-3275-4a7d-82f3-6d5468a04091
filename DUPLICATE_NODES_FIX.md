# 重复节点问题解决方案

## 问题分析

在运行 `ros2 node list` 时发现以下重复节点：
- `/costmap/costmap` (x2)
- `/global_costmap/global_costmap` (x2) 
- `/pathFollower` (x2)
- `/terrainAnalysis` (x2)

## 根本原因

1. **仿真环境冲突**: `integrated_navigation.launch.py` 在启用仿真模式 (`use_simulator:=true`) 时会：
   - 包含 `system_*.launch` 文件（启动仿真环境）
   - 同时直接启动核心节点
   - 导致相同节点被启动两次

2. **Nav2 Costmap 重复**: Nav2的 `planner_server` 和 `controller_server` 都会创建costmap节点，需要合适的命名空间配置

## 已实施的修复

### 1. 条件化节点启动

修改了 `src/integrated_navigation/launch/integrated_navigation.launch.py`:

```python
# 扩展地形分析（仅在非仿真模式下启动）
terrain_analysis_ext_cmd = Node(
    package='terrain_analysis_ext',
    executable='terrainAnalysisExt',
    name='terrainAnalysisExt',
    output='screen',
    parameters=[params_file],
    condition=UnlessCondition(use_simulator)  # 新增条件
)

# 可视化工具（仅在非仿真模式下启动）
visualization_tools_cmd = Node(
    package='visualization_tools',
    executable='visualizationTools',
    name='visualizationTools',
    output='screen',
    parameters=[params_file],
    condition=UnlessCondition(use_simulator)  # 新增条件
)
```

## 正确的使用方式

### 1. 仅算法模式（推荐用于实际机器人）
```bash
# 启动核心导航算法，无重复节点
ros2 launch integrated_navigation algorithm_only.launch.py

# 带地图文件
ros2 launch integrated_navigation algorithm_only.launch.py map_file:=/path/to/map.yaml
```

### 2. 完整仿真模式
```bash
# 启动完整系统（包括仿真环境）
ros2 launch integrated_navigation integrated_navigation.launch.py use_simulator:=true

# 不同环境
ros2 launch integrated_navigation integrated_navigation.launch.py use_simulator:=true world_file:=forest
```

### 3. 本地规划测试（无SMAC全局规划器）
```bash
# 仅本地规划和地形分析
ros2 launch integrated_navigation no_smac_test.launch.py
```

## 预防重复节点的最佳实践

1. **使用条件启动**: 使用 `IfCondition` 和 `UnlessCondition` 避免冲突
2. **合适的命名空间**: 为costmap节点设置正确的命名空间
3. **单一职责启动文件**: 每个launch文件专注特定功能
4. **参数化配置**: 使用launch参数控制节点启动

## 验证修复

启动系统后检查节点：
```bash
# 检查节点列表（应无重复警告）
ros2 node list

# 验证关键节点正常运行
ros2 topic hz /terrain_map    # 应 >2Hz
ros2 topic hz /path          # 应 >10Hz
ros2 topic hz /plan          # 全局规划频率
```

如果仍有重复，检查是否同时运行了多个launch文件。