#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <chrono>
#include <algorithm>

#include "rclcpp/rclcpp.hpp"
#include "rclcpp/time.hpp"
#include "rclcpp/clock.hpp"
#include "builtin_interfaces/msg/time.hpp"

#include "nav_msgs/msg/odometry.hpp"
#include "sensor_msgs/msg/point_cloud2.hpp"
#include <sensor_msgs/msg/joy.hpp>
#include <std_msgs/msg/float32.hpp>
#include <std_msgs/msg/float32_multi_array.hpp>
#include <std_msgs/msg/int8.hpp>
#include <nav_msgs/msg/path.hpp>
#include <geometry_msgs/msg/twist.h>
#include <sensor_msgs/msg/imu.h>

#include "tf2/transform_datatypes.h"
#include "tf2_ros/transform_broadcaster.h"
#include "tf2_geometry_msgs/tf2_geometry_msgs.hpp"


#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>

#include "message_filters/subscriber.h"
#include "message_filters/synchronizer.h"
#include "message_filters/sync_policies/approximate_time.h"
#include "rmw/types.h"
#include "rmw/qos_profiles.h"

using namespace std;

const double PI = 3.1415926;

double sensorOffsetX = 0;
double sensorOffsetY = 0;
int pubSkipNum = 1;
int pubSkipCount = 0;
bool twoWayDrive = true;
double lookAheadDis = 0.5;
double yawRateGain = 7.5;
double stopYawRateGain = 7.5;
double maxYawRate = 45.0;
double maxSpeed = 1.0;
double maxAccel = 1.0;
double switchTimeThre = 1.0;
double dirDiffThre = 0.1;
double stopDisThre = 0.2;
double slowDwnDisThre = 1.0;
bool useInclRateToSlow = false;
double inclRateThre = 120.0;
double slowRate1 = 0.25;
double slowRate2 = 0.5;
double slowTime1 = 2.0;
double slowTime2 = 2.0;
bool useInclToStop = false;
double inclThre = 45.0;
double stopTime = 5.0;
bool noRotAtStop = false;
bool noRotAtGoal = true;
bool autonomyMode = true;
double autonomySpeed = 1.0;
double joyToSpeedDelay = 2.0;


float joySpeed = 0;
float joySpeedRaw = 0;
float joyYaw = 0;
int safetyStop = 0;

float vehicleX = 0;
float vehicleY = 0;
float vehicleZ = 0;
float vehicleRoll = 0;
float vehiclePitch = 0;
float vehicleYaw = 0;

float vehicleXRec = 0;
float vehicleYRec = 0;
float vehicleZRec = 0;
float vehicleRollRec = 0;
float vehiclePitchRec = 0;
float vehicleYawRec = 0;

float vehicleYawRate = 0;
float vehicleSpeed = 0;

double odomTime = 0;
double joyTime = 0;
double slowInitTime = 0;
double stopInitTime = false;
int pathPointID = 0;
bool pathInit = false;
bool navFwd = true;
double switchTime = 0;

nav_msgs::msg::Path path;
rclcpp::Node::SharedPtr nh;


void odomHandler(const nav_msgs::msg::Odometry::ConstSharedPtr odomIn)
{
  odomTime = rclcpp::Time(odomIn->header.stamp).seconds();
  double roll, pitch, yaw;
  geometry_msgs::msg::Quaternion geoQuat = odomIn->pose.pose.orientation;
  tf2::Matrix3x3(tf2::Quaternion(geoQuat.x, geoQuat.y, geoQuat.z, geoQuat.w)).getRPY(roll, pitch, yaw);

  vehicleRoll = roll;
  vehiclePitch = pitch;
  vehicleYaw = yaw;
  vehicleX = odomIn->pose.pose.position.x - cos(yaw) * sensorOffsetX + sin(yaw) * sensorOffsetY;
  vehicleY = odomIn->pose.pose.position.y - sin(yaw) * sensorOffsetX - cos(yaw) * sensorOffsetY;
  vehicleZ = odomIn->pose.pose.position.z;

  if ((fabs(roll) > inclThre * PI / 180.0 || fabs(pitch) > inclThre * PI / 180.0) && useInclToStop) {
    stopInitTime = rclcpp::Time(odomIn->header.stamp).seconds();
  }

  if ((fabs(odomIn->twist.twist.angular.x) > inclRateThre * PI / 180.0 || fabs(odomIn->twist.twist.angular.y) > inclRateThre * PI / 180.0) && useInclRateToSlow) {
    slowInitTime = rclcpp::Time(odomIn->header.stamp).seconds();
  }
}

void pathHandler(const nav_msgs::msg::Path::ConstSharedPtr pathIn)
{
  int pathSize = pathIn->poses.size();

  // 打印接收到的路径信息
  RCLCPP_INFO(nh->get_logger(), "=== Received /path data ===");
  RCLCPP_INFO(nh->get_logger(), "Path size: %d points", pathSize);
  RCLCPP_INFO(nh->get_logger(), "Frame ID: %s", pathIn->header.frame_id.c_str());
  RCLCPP_INFO(nh->get_logger(), "Timestamp: %ld.%09ld",
              pathIn->header.stamp.sec, pathIn->header.stamp.nanosec);

  // 打印前几个路径点的详细信息
  int printCount = std::min(pathSize, 5); // 最多打印前5个点
  for (int i = 0; i < printCount; i++) {
    RCLCPP_INFO(nh->get_logger(), "Point[%d]: x=%.3f, y=%.3f, z=%.3f",
                i,
                pathIn->poses[i].pose.position.x,
                pathIn->poses[i].pose.position.y,
                pathIn->poses[i].pose.position.z);
  }

  if (pathSize > 5) {
    RCLCPP_INFO(nh->get_logger(), "... and %d more points", pathSize - 5);
  }

  // 如果路径不为空，打印起点和终点
  if (pathSize > 0) {
    RCLCPP_INFO(nh->get_logger(), "Start point: (%.3f, %.3f, %.3f)",
                pathIn->poses[0].pose.position.x,
                pathIn->poses[0].pose.position.y,
                pathIn->poses[0].pose.position.z);

    if (pathSize > 1) {
      RCLCPP_INFO(nh->get_logger(), "End point: (%.3f, %.3f, %.3f)",
                  pathIn->poses[pathSize-1].pose.position.x,
                  pathIn->poses[pathSize-1].pose.position.y,
                  pathIn->poses[pathSize-1].pose.position.z);
    }
  }
  RCLCPP_INFO(nh->get_logger(), "========================");

  path.poses.resize(pathSize);
  for (int i = 0; i < pathSize; i++) {
    path.poses[i].pose.position.x = pathIn->poses[i].pose.position.x;
    path.poses[i].pose.position.y = pathIn->poses[i].pose.position.y;
    path.poses[i].pose.position.z = pathIn->poses[i].pose.position.z;
  }

  vehicleXRec = vehicleX;
  vehicleYRec = vehicleY;
  vehicleZRec = vehicleZ;
  vehicleRollRec = vehicleRoll;
  vehiclePitchRec = vehiclePitch;
  vehicleYawRec = vehicleYaw;

  pathPointID = 0;
  pathInit = true;
}

void joystickHandler(const sensor_msgs::msg::Joy::ConstSharedPtr joy)
{
  joyTime = nh->now().seconds(); 
  joySpeedRaw = sqrt(joy->axes[3] * joy->axes[3] + joy->axes[4] * joy->axes[4]);
  joySpeed = joySpeedRaw;
  if (joySpeed > 1.0) joySpeed = 1.0;
  if (joy->axes[4] == 0) joySpeed = 0;
  joyYaw = joy->axes[3];
  if (joySpeed == 0 && noRotAtStop) joyYaw = 0;

  if (joy->axes[4] < 0 && !twoWayDrive) {
    joySpeed = 0;
    joyYaw = 0;
  }

  if (joy->axes[2] > -0.1) {
    autonomyMode = false;
  } else {
    autonomyMode = true;
  }
}

void speedHandler(const std_msgs::msg::Float32::ConstSharedPtr speed)
{
  double speedTime = nh->now().seconds();
  if (autonomyMode && speedTime - joyTime > joyToSpeedDelay && joySpeedRaw == 0) {
    joySpeed = speed->data / maxSpeed;

    if (joySpeed < 0) joySpeed = 0;
    else if (joySpeed > 1.0) joySpeed = 1.0;
  }
}

void stopHandler(const std_msgs::msg::Int8::ConstSharedPtr stop)
{
  safetyStop = stop->data;
}

int main(int argc, char** argv)
{
  rclcpp::init(argc, argv);
  nh = rclcpp::Node::make_shared("pathFollower");

  nh->declare_parameter<double>("sensorOffsetX", 0.0);
  nh->declare_parameter<double>("sensorOffsetY", 0.0);
  nh->declare_parameter<int>("pubSkipNum", 1);
  nh->declare_parameter<bool>("twoWayDrive", true);
  nh->declare_parameter<double>("lookAheadDis", 0.5);
  nh->declare_parameter<double>("yawRateGain", 7.5);
  nh->declare_parameter<double>("stopYawRateGain", 7.5);
  nh->declare_parameter<double>("maxYawRate", 45.0);
  nh->declare_parameter<double>("maxSpeed", 1.0);
  nh->declare_parameter<double>("maxAccel", 1.0);
  nh->declare_parameter<double>("switchTimeThre", 1.0);
  nh->declare_parameter<double>("dirDiffThre", 0.1);
  nh->declare_parameter<double>("stopDisThre", 0.2);
  nh->declare_parameter<double>("slowDwnDisThre", 1.0);
  nh->declare_parameter<bool>("useInclRateToSlow", false);
  nh->declare_parameter<double>("inclRateThre", 120.0);
  nh->declare_parameter<double>("slowRate1", 0.25);
  nh->declare_parameter<double>("slowRate2", 0.5);
  nh->declare_parameter<double>("slowTime1", 2.0);
  nh->declare_parameter<double>("slowTime2", 2.0);
  nh->declare_parameter<bool>("useInclToStop", false);
  nh->declare_parameter<double>("inclThre", 45.0);
  nh->declare_parameter<double>("stopTime", 5.0);
  nh->declare_parameter<bool>("noRotAtStop", false);
  nh->declare_parameter<bool>("noRotAtGoal", true);
  nh->declare_parameter<bool>("autonomyMode", false);
  nh->declare_parameter<double>("autonomySpeed", 1.0);
  nh->declare_parameter<double>("joyToSpeedDelay", 2.0);

  // 从参数服务器获取参数值（这些值将由launch文件从YAML文件加载）
  sensorOffsetX = nh->get_parameter("sensorOffsetX").as_double();
  sensorOffsetY = nh->get_parameter("sensorOffsetY").as_double();
  pubSkipNum = nh->get_parameter("pubSkipNum").as_int();
  twoWayDrive = nh->get_parameter("twoWayDrive").as_bool();
  lookAheadDis = nh->get_parameter("lookAheadDis").as_double();
  yawRateGain = nh->get_parameter("yawRateGain").as_double();
  stopYawRateGain = nh->get_parameter("stopYawRateGain").as_double();
  maxYawRate = nh->get_parameter("maxYawRate").as_double();
  maxSpeed = nh->get_parameter("maxSpeed").as_double();
  maxAccel = nh->get_parameter("maxAccel").as_double();
  switchTimeThre = nh->get_parameter("switchTimeThre").as_double();
  dirDiffThre = nh->get_parameter("dirDiffThre").as_double();
  stopDisThre = nh->get_parameter("stopDisThre").as_double();
  slowDwnDisThre = nh->get_parameter("slowDwnDisThre").as_double();
  useInclRateToSlow = nh->get_parameter("useInclRateToSlow").as_bool();
  inclRateThre = nh->get_parameter("inclRateThre").as_double();
  slowRate1 = nh->get_parameter("slowRate1").as_double();
  slowRate2 = nh->get_parameter("slowRate2").as_double();
  slowTime1 = nh->get_parameter("slowTime1").as_double();
  slowTime2 = nh->get_parameter("slowTime2").as_double();
  useInclToStop = nh->get_parameter("useInclToStop").as_bool();
  inclThre = nh->get_parameter("inclThre").as_double();
  stopTime = nh->get_parameter("stopTime").as_double();
  noRotAtStop = nh->get_parameter("noRotAtStop").as_bool();
  noRotAtGoal = nh->get_parameter("noRotAtGoal").as_bool();
  autonomyMode = nh->get_parameter("autonomyMode").as_bool();
  autonomySpeed = nh->get_parameter("autonomySpeed").as_double();
  joyToSpeedDelay = nh->get_parameter("joyToSpeedDelay").as_double();

  auto subOdom = nh->create_subscription<nav_msgs::msg::Odometry>("/state_estimation", 5, odomHandler);

  auto subPath = nh->create_subscription<nav_msgs::msg::Path>("/path", 5, pathHandler);

  auto subJoystick = nh->create_subscription<sensor_msgs::msg::Joy>("/joy", 5, joystickHandler);

  auto subSpeed = nh->create_subscription<std_msgs::msg::Float32>("/speed", 5, speedHandler);

  auto subStop = nh->create_subscription<std_msgs::msg::Int8>("/stop", 5, stopHandler);

  auto pubSpeed = nh->create_publisher<geometry_msgs::msg::Twist>("/cmd_vel", 5);

  
  geometry_msgs::msg::Twist cmd_vel;

  if (autonomyMode) {
    joySpeed = autonomySpeed / maxSpeed;

    if (joySpeed < 0) joySpeed = 0;
    else if (joySpeed > 1.0) joySpeed = 1.0;
  }

  rclcpp::Rate rate(100);
  bool status = rclcpp::ok();
  while (status) {
    rclcpp::spin_some(nh);

    if (pathInit) {
      float vehicleXRel = cos(vehicleYawRec) * (vehicleX - vehicleXRec) 
                        + sin(vehicleYawRec) * (vehicleY - vehicleYRec);
      float vehicleYRel = -sin(vehicleYawRec) * (vehicleX - vehicleXRec) 
                        + cos(vehicleYawRec) * (vehicleY - vehicleYRec);

      int pathSize = path.poses.size();
      float endDisX = path.poses[pathSize - 1].pose.position.x - vehicleXRel;
      float endDisY = path.poses[pathSize - 1].pose.position.y - vehicleYRel;
      float endDis = sqrt(endDisX * endDisX + endDisY * endDisY);

      float disX, disY, dis;
      while (pathPointID < pathSize - 1) {
        disX = path.poses[pathPointID].pose.position.x - vehicleXRel;
        disY = path.poses[pathPointID].pose.position.y - vehicleYRel;
        dis = sqrt(disX * disX + disY * disY);
        if (dis < lookAheadDis) {
          pathPointID++;
        } else {
          break;
        }
      }

      disX = path.poses[pathPointID].pose.position.x - vehicleXRel;
      disY = path.poses[pathPointID].pose.position.y - vehicleYRel;
      dis = sqrt(disX * disX + disY * disY);
      float pathDir = atan2(disY, disX);

      float dirDiff = vehicleYaw - vehicleYawRec - pathDir;
      if (dirDiff > PI) dirDiff -= 2 * PI;
      else if (dirDiff < -PI) dirDiff += 2 * PI;
      if (dirDiff > PI) dirDiff -= 2 * PI;
      else if (dirDiff < -PI) dirDiff += 2 * PI;

      if (twoWayDrive) {
        double time = nh->now().seconds();
        if (fabs(dirDiff) > PI / 2 && navFwd && time - switchTime > switchTimeThre) {
          navFwd = false;
          switchTime = time;
        } else if (fabs(dirDiff) < PI / 2 && !navFwd && time - switchTime > switchTimeThre) {
          navFwd = true;
          switchTime = time;
        }
      }

      float joySpeed2 = maxSpeed * joySpeed;
      if (!navFwd) {
        dirDiff += PI;
        if (dirDiff > PI) dirDiff -= 2 * PI;
        joySpeed2 *= -1;
      }

      if (fabs(vehicleSpeed) < 2.0 * maxAccel / 100.0) vehicleYawRate = -stopYawRateGain * dirDiff;
      else vehicleYawRate = -yawRateGain * dirDiff;

      if (vehicleYawRate > maxYawRate * PI / 180.0) vehicleYawRate = maxYawRate * PI / 180.0;
      else if (vehicleYawRate < -maxYawRate * PI / 180.0) vehicleYawRate = -maxYawRate * PI / 180.0;

      if (joySpeed2 == 0 && !autonomyMode) {
        vehicleYawRate = maxYawRate * joyYaw * PI / 180.0;
      } else if (pathSize <= 1 || (dis < stopDisThre && noRotAtGoal)) {
        vehicleYawRate = 0;
      }

      if (pathSize <= 1) {
        joySpeed2 = 0;
      } else if (endDis / slowDwnDisThre < joySpeed) {
        joySpeed2 *= endDis / slowDwnDisThre;
      }

      float joySpeed3 = joySpeed2;
      if (odomTime < slowInitTime + slowTime1 && slowInitTime > 0) joySpeed3 *= slowRate1;
      else if (odomTime < slowInitTime + slowTime1 + slowTime2 && slowInitTime > 0) joySpeed3 *= slowRate2;

      if (fabs(dirDiff) < dirDiffThre && dis > stopDisThre) {
        if (vehicleSpeed < joySpeed3) vehicleSpeed += maxAccel / 100.0;
        else if (vehicleSpeed > joySpeed3) vehicleSpeed -= maxAccel / 100.0;
      } else {
        if (vehicleSpeed > 0) vehicleSpeed -= maxAccel / 100.0;
        else if (vehicleSpeed < 0) vehicleSpeed += maxAccel / 100.0;
      }

      if (odomTime < stopInitTime + stopTime && stopInitTime > 0) {
        vehicleSpeed = 0;
        vehicleYawRate = 0;
      }

      if (safetyStop >= 1) vehicleSpeed = 0;
      if (safetyStop >= 2) vehicleYawRate = 0;

      pubSkipCount--;
      if (pubSkipCount < 0) {
        if (fabs(vehicleSpeed) <= maxAccel / 100.0) cmd_vel.linear.x = 0;
        else cmd_vel.linear.x = vehicleSpeed;
        cmd_vel.angular.z = vehicleYawRate;
        pubSpeed->publish(cmd_vel);

        pubSkipCount = pubSkipNum;
      }
    }

    status = rclcpp::ok();
    rate.sleep();
  }

  return 0;
}
