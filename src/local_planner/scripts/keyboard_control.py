#!/usr/bin/env python3

import threading
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist, TwistStamped


class _Getch:
    """
    Gets a single character from standard input. 
    Does not echo to the screen.
    """

    def __init__(self):
        try:
            self.impl = _GetchWindows()
        except ImportError:
            self.impl = _GetchUnix()

    def __call__(self):
        return self.impl()


class _GetchUnix:
    def __init__(self):
        import tty
        import sys

    def __call__(self):
        import sys
        import tty
        import termios
        fd = sys.stdin.fileno()
        old_settings = termios.tcgetattr(fd)
        try:
            tty.setraw(sys.stdin.fileno())
            ch = sys.stdin.read(1)
        finally:
            termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)
        return ch


class _GetchWindows:
    def __init__(self):
        import msvcrt

    def __call__(self):
        import msvcrt
        return msvcrt.getch()


class TeleopPublisher(Node):

    def __init__(self):
        super().__init__('keyboard_control_node')
        # 发布TwistStamped消息类型以匹配pathFollower期望的格式
        self.vel_publisher = self.create_publisher(TwistStamped, '/cmd_vel', 10)
        timer_period = 0.1  # 10Hz
        self.timer = self.create_timer(timer_period, self.velocity_publish_event)
        self.cmd_vel_msg = TwistStamped()
        self.cmd_vel_msg.header.frame_id = "vehicle"

    def set_vel(self, v, w):
        self.get_logger().info("Linear: %.2f m/s, Angular: %.2f rad/s" % (v, w))
        self.cmd_vel_msg.twist.linear.x = v
        self.cmd_vel_msg.twist.angular.z = w

    def velocity_publish_event(self):
        self.cmd_vel_msg.header.stamp = self.get_clock().now().to_msg()
        self.vel_publisher.publish(self.cmd_vel_msg)


def main(args=None):
    rclpy.init(args=args)
    getch = _Getch()
    publish_node = TeleopPublisher()
    thread = threading.Thread(target=rclpy.spin,
                              args=(publish_node, ), daemon=True)
    # Thread for node's timer callback
    thread.start()
    v = 0.0
    w = 0.0
    publish_node.get_logger().info("""
控制说明:
    w: 增加线速度 (+0.1 m/s)
    s: 减少线速度 (-0.1 m/s)
    a: 增加角速度 (+0.1 rad/s) - 左转
    d: 减少角速度 (-0.1 rad/s) - 右转
    空格键: 停止 (速度归零)
    q: 退出程序
    
注意: 确保pathFollower节点的autonomyMode设为false以启用手动控制
""")
    try:
        while (rclpy.ok()):
            key_in = getch()
            if key_in == "w":
                v += 0.1
            elif key_in == "s":
                v -= 0.1
            elif key_in == "d":
                w -= 0.1
            elif key_in == "a":
                w += 0.1
            elif key_in == " ":  # 空格键停止
                v = 0.0
                w = 0.0
            elif key_in == "q":
                break
            # 限制速度范围
            v = max(-2.0, min(2.0, v))  # 限制线速度在±2 m/s
            w = max(-2.0, min(2.0, w))  # 限制角速度在±2 rad/s
            publish_node.set_vel(v, w)
    except KeyboardInterrupt:
        pass

    rclpy.shutdown()
    thread.join()


if __name__ == "__main__":
    main()
