# 整合导航系统配置文件
# 结合SMAC全局规划器和本地路径规划器

# AMCL 本地化
amcl:
  ros__parameters:
    use_sim_time: True
    alpha1: 0.2
    alpha2: 0.2
    alpha3: 0.2
    alpha4: 0.2
    alpha5: 0.2
    base_frame_id: "base_link"
    beam_skip_distance: 0.5
    beam_skip_error_threshold: 0.9
    beam_skip_threshold: 0.3
    do_beamskip: false
    global_frame_id: "map"
    lambda_short: 0.1
    laser_likelihood_max_dist: 2.0
    laser_max_range: 100.0
    laser_min_range: -1.0
    laser_model_type: "likelihood_field"
    max_beams: 60
    max_particles: 2000
    min_particles: 500
    odom_frame_id: "odom"
    pf_err: 0.05
    pf_z: 0.99
    recovery_alpha_fast: 0.0
    recovery_alpha_slow: 0.0
    resample_interval: 1
    robot_model_type: "nav2_amcl::DifferentialMotionModel"
    save_pose_rate: 0.5
    sigma_hit: 0.2
    tf_broadcast: true
    transform_tolerance: 1.0
    update_min_a: 0.2
    update_min_d: 0.25
    z_hit: 0.5
    z_max: 0.05
    z_rand: 0.5
    z_short: 0.05
    scan_topic: scan

# 行为树导航器
bt_navigator:
  ros__parameters:
    use_sim_time: True
    global_frame: map
    robot_base_frame: base_link
    odom_topic: /odom
    bt_loop_duration: 10
    default_server_timeout: 20
    plugin_lib_names:
    - nav2_compute_path_to_pose_action_bt_node
    - nav2_compute_path_through_poses_action_bt_node
    - nav2_smooth_path_action_bt_node
    - nav2_follow_path_action_bt_node
    - nav2_spin_action_bt_node
    - nav2_wait_action_bt_node
    - nav2_back_up_action_bt_node
    - nav2_drive_on_heading_bt_node
    - nav2_clear_costmap_service_bt_node
    - nav2_is_stuck_condition_bt_node
    - nav2_goal_reached_condition_bt_node
    - nav2_goal_updated_condition_bt_node
    - nav2_globally_updated_goal_condition_bt_node
    - nav2_is_path_valid_condition_bt_node
    - nav2_initial_pose_received_condition_bt_node
    - nav2_reinitialize_global_localization_service_bt_node
    - nav2_rate_controller_bt_node
    - nav2_distance_controller_bt_node
    - nav2_speed_controller_bt_node
    - nav2_truncate_path_action_bt_node
    - nav2_truncate_path_local_action_bt_node
    - nav2_goal_updater_node_bt_node
    - nav2_recovery_node_bt_node
    - nav2_pipeline_sequence_bt_node
    - nav2_round_robin_node_bt_node
    - nav2_transform_available_condition_bt_node
    - nav2_time_expired_condition_bt_node
    - nav2_path_expiring_timer_condition
    - nav2_distance_traveled_condition_bt_node
    - nav2_single_trigger_bt_node
    - nav2_goal_updated_controller_bt_node
    - nav2_is_battery_low_condition_bt_node
    - nav2_navigate_through_poses_action_bt_node
    - nav2_navigate_to_pose_action_bt_node
    - nav2_remove_passed_goals_action_bt_node
    - nav2_planner_selector_bt_node
    - nav2_controller_selector_bt_node
    - nav2_goal_checker_selector_bt_node
    - nav2_controller_cancel_bt_node
    - nav2_path_longer_on_approach_bt_node
    - nav2_wait_cancel_bt_node
    - nav2_spin_cancel_bt_node
    - nav2_back_up_cancel_bt_node
    - nav2_drive_on_heading_cancel_bt_node

# 控制器服务器 - 使用自定义局部规划器
controller_server:
  ros__parameters:
    use_sim_time: True
    controller_frequency: 20.0
    min_x_velocity_threshold: 0.001
    min_y_velocity_threshold: 0.5
    min_theta_velocity_threshold: 0.001
    failure_tolerance: 0.3
    progress_checker_plugin: "progress_checker"
    goal_checker_plugins: ["general_goal_checker"] 
    controller_plugins: ["LocalPlanner"]

    # Progress checker parameters
    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.5
      movement_time_allowance: 10.0
    
    # Goal checker parameters
    general_goal_checker:
      stateful: True
      plugin: "nav2_controller::SimpleGoalChecker"
      xy_goal_tolerance: 0.25
      yaw_goal_tolerance: 0.25
    
    # 使用DWB局部规划器（临时替代）
    LocalPlanner:
      plugin: "dwb_core::DWBLocalPlanner"  # 使用标准DWB控制器
      debug_trajectory_details: True
      min_vel_x: 0.0
      min_vel_y: 0.0
      max_vel_x: 0.26
      max_vel_y: 0.0
      max_vel_theta: 1.0
      min_speed_xy: 0.0
      max_speed_xy: 0.26
      min_speed_theta: 0.0
      acc_lim_x: 2.5
      acc_lim_y: 0.0
      acc_lim_theta: 3.2
      decel_lim_x: -2.5
      decel_lim_y: 0.0
      decel_lim_theta: -3.2
      vx_samples: 20
      vy_samples: 5
      vtheta_samples: 20
      sim_time: 1.7
      linear_granularity: 0.05
      angular_granularity: 0.025
      transform_tolerance: 0.2
      xy_goal_tolerance: 0.25
      trans_stopped_velocity: 0.25
      short_circuit_trajectory_evaluation: True
      stateful: True
      critics: ["RotateToGoal", "Oscillation", "BaseObstacle", "GoalAlign", "PathAlign", "PathDist", "GoalDist"]
      BaseObstacle.scale: 0.02
      PathAlign.scale: 32.0
      PathAlign.forward_point_distance: 0.1
      GoalAlign.scale: 24.0
      GoalAlign.forward_point_distance: 0.1
      PathDist.scale: 32.0
      GoalDist.scale: 24.0
      RotateToGoal.scale: 32.0
      RotateToGoal.slowing_factor: 5.0
      RotateToGoal.lookahead_time: -1.0
      max_vel_y: 0.0
      max_vel_theta: 1.0
      min_speed_xy: 0.0
      max_speed_xy: 1.0
      min_speed_theta: 0.0
      acc_lim_x: 2.5
      acc_lim_y: 0.0
      acc_lim_theta: 3.2
      decel_lim_x: -2.5
      decel_lim_y: 0.0
      decel_lim_theta: -3.2
      transform_tolerance: 0.2
      xy_goal_tolerance: 0.25

# 局部代价地图
local_costmap:
  ros__parameters:
      update_frequency: 5.0
      publish_frequency: 2.0
      global_frame: odom
      robot_base_frame: base_link
      use_sim_time: True
      rolling_window: true
      width: 6
      height: 6
      resolution: 0.05
      robot_radius: 0.22
      plugins: ["voxel_layer", "inflation_layer"]
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 3.0
        inflation_radius: 0.55
      voxel_layer:
        plugin: "nav2_costmap_2d::VoxelLayer"
        enabled: True
        publish_voxel_map: True
        origin_z: 0.0
        z_resolution: 0.05
        z_voxels: 16
        max_obstacle_height: 2.0
        mark_threshold: 0
        observation_sources: scan pointcloud
        scan:
          topic: /scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 3.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.5
          obstacle_min_range: 0.0
        pointcloud:
          topic: /registered_scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "PointCloud2"
          raytrace_max_range: 3.5
          raytrace_min_range: 0.0
          obstacle_max_range: 3.0
          obstacle_min_range: 0.0
      always_send_full_costmap: True

# 全局代价地图
global_costmap:
  ros__parameters:
      update_frequency: 1.0
      publish_frequency: 1.0
      global_frame: map
      robot_base_frame: base_link
      use_sim_time: True
      robot_radius: 0.22
      resolution: 0.05
      track_unknown_space: true
      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        observation_sources: scan pointcloud
        scan:
          topic: /scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 3.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.5
          obstacle_min_range: 0.0
        pointcloud:
          topic: /registered_scan
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "PointCloud2"
          raytrace_max_range: 3.5
          raytrace_min_range: 0.0
          obstacle_max_range: 3.0
          obstacle_min_range: 0.0
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 3.0
        inflation_radius: 0.55
      always_send_full_costmap: True

# 地图服务器
map_server:
  ros__parameters:
    use_sim_time: false  # 由launch文件动态设置
    # yaml_filename由launch文件参数传递，这里不覆盖

# SMAC全局规划器
planner_server:
  ros__parameters:
    expected_planner_frequency: 20.0
    use_sim_time: True
    planner_plugins: ["GridBased"]
    GridBased:
      plugin: "nav2_smac_planner/SmacPlannerHybrid"
      
      # 性能设置
      downsample_costmap: false
      downsampling_factor: 1
      tolerance: 0.25
      allow_unknown: true
      max_iterations: 1000000
      max_on_approach_iterations: 1000
      max_planning_time: 5.0
      
      # 运动模型设置
      motion_model_for_search: "DUBIN"
      angle_quantization_bins: 36
      
      # 解析扩展设置
      analytic_expansion_ratio: 4.5
      analytic_expansion_max_length: 4.0
      analytic_expansion_max_cost: 300.0
      
      # 车辆约束
      minimum_turning_radius: 0.30
      
      # 代价惩罚
      reverse_penalty: 3.0
      change_penalty: 0.5
      non_straight_penalty: 0.8
      cost_penalty: 1.5
      retrospective_penalty: 0.025
      
      # 查找表设置
      lookup_table_size: 20.0
      cache_obstacle_heuristic: false
      
      # 后处理
      smooth_path: True
      
      # 调试设置
      debug_visualizations: True

# 路径平滑器
smoother_server:
  ros__parameters:
    use_sim_time: True
    smoother_plugins: ["simple_smoother"]
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"
      tolerance: 1.0e-12
      max_its: 2000
      do_refinement: True
      w_smooth: 0.3
      w_data: 0.2
      w_dynamic_obstacle: 0.25

# 行为服务器
behavior_server:
  ros__parameters:
    costmap_topic: local_costmap/costmap_raw
    footprint_topic: local_costmap/published_footprint
    cycle_frequency: 10.0
    behavior_plugins: ["spin", "backup", "drive_on_heading", "wait"]
    spin:
      plugin: "nav2_behaviors/Spin"
    backup:
      plugin: "nav2_behaviors/BackUp"
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading"
    wait:
      plugin: "nav2_behaviors/Wait"
    global_frame: odom
    robot_base_frame: base_link
    transform_tolerance: 0.1
    use_sim_time: true
    simulate_ahead_time: 2.0
    max_rotational_vel: 1.0
    min_rotational_vel: 0.4
    rotational_acc_lim: 3.2

# 机器人状态发布器
robot_state_publisher:
  ros__parameters:
    use_sim_time: True

# 航点跟随器
waypoint_follower:
  ros__parameters:
    use_sim_time: True
    loop_rate: 20
    stop_on_failure: false
    waypoint_task_executor_plugin: "wait_at_waypoint"
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True
      waypoint_pause_duration: 200

# 速度平滑器
velocity_smoother:
  ros__parameters:
    use_sim_time: True
    smoothing_frequency: 20.0
    scale_velocities: False
    feedback: "OPEN_LOOP"
    max_velocity: [1.0, 0.0, 1.0]
    min_velocity: [-1.0, 0.0, -1.0]
    max_accel: [2.5, 0.0, 3.2]
    max_decel: [-2.5, 0.0, -3.2]
    odom_topic: "odom"
    odom_duration: 0.1
    deadband_velocity: [0.0, 0.0, 0.0]
    velocity_timeout: 1.0

# 本地规划器参数（与原autonomous_exploration兼容）
localPlanner:
  ros__parameters:
    use_sim_time: True
    # 路径文件夹路径
    pathFolder: "/home/<USER>/saoxueche/install/local_planner/share/local_planner/paths"
    
    # 路径规划参数
    pathNum: 343
    groupNum: 7
    gridVoxelSize: 0.02
    searchRadius: 0.45
    gridVoxelOffsetX: 3.2
    gridVoxelOffsetY: 4.5
    
    # 车辆参数
    vehicleLength: 3.6
    vehicleWidth: 1.8
    sensorOffsetX: 0.0
    sensorOffsetY: 0.0
    twoWayDrive: true
    
    # 地形和障碍物检测
    laserVoxelSize: 0.05
    terrainVoxelSize: 0.2
    useTerrainAnalysis: false
    checkObstacle: true
    adjacentRange: 3.5
    obstacleHeightThre: 0.2
    groundHeightThre: 0.1
    pointPerPathThre: 2
    minRelZ: -0.5
    maxRelZ: 0.25
    
    # 速度和方向控制
    maxSpeed: 2.0
    dirWeight: 0.02
    dirThre: 90.0
    pathScale: 1.0
    minPathScale: 0.75
    pathScaleStep: 0.25
    pathScaleBySpeed: true
    minPathRange: 1.0
    pathRangeStep: 0.5
    pathRangeBySpeed: true
    pathCropByGoal: true
    
    # 自主导航
    autonomyMode: true
    autonomySpeed: 1.0
    goalClearRange: 0.5

# 地形分析参数
terrainAnalysis:
  ros__parameters:
    use_sim_time: True
    # 体素化参数
    scanVoxelSize: 0.05
    terrainVoxelSize: 0.2
    terrainVoxelWidth: 251
    planarVoxelWidth: 51
    
    # 时间衰减
    decayTime: 2.0
    noDecayDis: 4.0
    clearingDis: 8.0
    useSorting: true
    
    # 高程计算
    quantileZ: 0.25
    vehicleHeight: 1.5
    localRange: 5.0
    disRatioZ: 0.2
    
    # 更新控制
    voxelPointUpdateThre: 100
    voxelTimeUpdateThre: 2.0
    filterGroundVoxel: 0.1
    filterNegObsVoxel: true
    clearObstacle: false
    clearObstacleHeight: 0.5

# 路径跟踪器参数
pathFollower:
  ros__parameters:
    use_sim_time: True
    # 基础配置
    sensorOffsetX: 0.0
    sensorOffsetY: 0.0
    pubSkipNum: 1
    twoWayDrive: true
    
    # 路径跟踪参数
    lookAheadDis: 1.5
    yawRateGain: 7.5
    stopYawRateGain: 7.5
    maxYawRate: 45.0
    maxSpeed: 2.0
    maxAccel: 2.5
    switchTimeThre: 1.0
    dirDiffThre: 0.1
    stopDisThre: 0.2
    slowDwnDisThre: 1.0
    
    # 安全控制参数
    useInclRateToSlow: false
    inclRateThre: 120.0
    slowRate1: 0.25
    slowRate2: 0.5
    slowTime1: 2.0
    slowTime2: 2.0
    useInclToStop: false
    inclThre: 45.0
    stopTime: 5.0
    noRotAtStop: false
    noRotAtGoal: true
    
    # 自主模式参数
    autonomyMode: true
    autonomySpeed: 1.0
    joyToSpeedDelay: 2.0