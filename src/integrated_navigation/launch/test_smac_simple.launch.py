#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    # 包目录
    nav2_smac_pkg = FindPackageShare('nav2_smac_test')
    
    # Launch配置变量
    use_sim_time = LaunchConfiguration('use_sim_time')
    map_file = LaunchConfiguration('map_file')
    
    # 默认路径
    default_map_file = PathJoinSubstitution([
        nav2_smac_pkg, 'maps', 'test_map.yaml'
    ])
    
    # Launch参数声明
    declare_use_sim_time_cmd = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time if true'
    )
    
    declare_map_file_cmd = DeclareLaunchArgument(
        'map_file',
        default_value=default_map_file,
        description='Full path to map yaml file'
    )
    
    # 地图服务器
    map_server_cmd = Node(
        package='nav2_map_server',
        executable='map_server',
        name='map_server',
        output='screen',
        parameters=[
            {'use_sim_time': use_sim_time},
            {'yaml_filename': map_file}
        ]
    )
    
    # SMAC全局规划器（简化配置）
    planner_server_cmd = Node(
        package='nav2_planner',
        executable='planner_server',
        name='planner_server',
        output='screen',
        parameters=[
            {
                'use_sim_time': use_sim_time,
                'planner_plugins': ['GridBased'],
                'GridBased.plugin': 'nav2_smac_planner/SmacPlannerHybrid',
                'GridBased.tolerance': 0.25,
                'GridBased.allow_unknown': True,
                'GridBased.max_iterations': 100000,
                'GridBased.max_planning_time': 5.0,
                'GridBased.motion_model_for_search': 'DUBIN',
                'GridBased.angle_quantization_bins': 36,
                'GridBased.minimum_turning_radius': 0.30,
                'GridBased.reverse_penalty': 3.0,
                'GridBased.smooth_path': True
            }
        ]
    )
    
    # 简化的全局代价地图
    global_costmap_cmd = Node(
        package='nav2_costmap_2d',
        executable='nav2_costmap_2d',
        name='global_costmap',
        output='screen',
        parameters=[
            {
                'use_sim_time': use_sim_time,
                'global_costmap.global_frame': 'map',
                'global_costmap.robot_base_frame': 'base_link',
                'global_costmap.resolution': 0.05,
                'global_costmap.track_unknown_space': True,
                'global_costmap.plugins': ['static_layer'],
                'global_costmap.static_layer.plugin': 'nav2_costmap_2d::StaticLayer',
                'global_costmap.static_layer.map_subscribe_transient_local': True,
                'global_costmap.always_send_full_costmap': True
            }
        ]
    )
    
    # 静态变换发布器
    static_tf_map_odom = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        arguments=['0', '0', '0', '0', '0', '0', 'map', 'odom']
    )
    
    static_tf_odom_base = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        arguments=['0', '0', '0', '0', '0', '0', 'odom', 'base_link']
    )
    
    # 生命周期管理器
    lifecycle_manager_cmd = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_navigation',
        output='screen',
        parameters=[
            {
                'use_sim_time': use_sim_time,
                'autostart': True,
                'node_names': [
                    'map_server',
                    'planner_server',
                    'global_costmap'
                ]
            }
        ]
    )
    
    # 组织启动描述
    ld = LaunchDescription()
    
    # 添加参数声明
    ld.add_action(declare_use_sim_time_cmd)
    ld.add_action(declare_map_file_cmd)
    
    # 添加节点
    ld.add_action(static_tf_map_odom)
    ld.add_action(static_tf_odom_base)
    ld.add_action(map_server_cmd)
    ld.add_action(global_costmap_cmd)
    ld.add_action(planner_server_cmd)
    ld.add_action(lifecycle_manager_cmd)
    
    return ld