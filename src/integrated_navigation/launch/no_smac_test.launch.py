#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node, SetParameter
from launch_ros.substitutions import FindPackageShare
from launch.conditions import IfCondition


def generate_launch_description():
    # 包目录
    integrated_nav_pkg = FindPackageShare('integrated_navigation')
    
    # Launch配置变量
    use_sim_time = LaunchConfiguration('use_sim_time')
    params_file = LaunchConfiguration('params_file')
    use_rviz = LaunchConfiguration('use_rviz')
    rviz_config = LaunchConfiguration('rviz_config')
    use_simulator = LaunchConfiguration('use_simulator')
    world_file = LaunchConfiguration('world_file')
    
    # 默认路径
    default_params_file = PathJoinSubstitution([
        integrated_nav_pkg, 'config', 'integrated_nav_params.yaml'
    ])
    default_rviz_config = PathJoinSubstitution([
        FindPackageShare('vehicle_simulator'), 'rviz', 'vehicle_simulator.rviz'
    ])
    
    # Launch参数声明
    declare_use_sim_time_cmd = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time if true'
    )
    
    declare_params_file_cmd = DeclareLaunchArgument(
        'params_file',
        default_value=default_params_file,
        description='Full path to navigation parameters file'
    )
    
    declare_use_rviz_cmd = DeclareLaunchArgument(
        'use_rviz',
        default_value='true',
        description='Whether to start RViz'
    )
    
    declare_rviz_config_cmd = DeclareLaunchArgument(
        'rviz_config',
        default_value=default_rviz_config,
        description='Full path to RViz config file'
    )
    
    declare_use_simulator_cmd = DeclareLaunchArgument(
        'use_simulator',
        default_value='false',
        description='Whether to start Gazebo simulator'
    )
    
    declare_world_file_cmd = DeclareLaunchArgument(
        'world_file',
        default_value='garage',
        description='World file name (garage, forest, campus, indoor, tunnel)'
    )
    
    # 设置全局参数
    set_use_sim_time = SetParameter('use_sim_time', use_sim_time)
    
    # ============ 保留的非SMAC模块 ============
    
    # 本地路径规划器
    local_planner_cmd = Node(
        package='local_planner',
        executable='localPlanner',
        name='localPlanner',
        output='screen',
        parameters=[params_file]
    )
    
    # 路径跟踪器
    path_follower_cmd = Node(
        package='local_planner',
        executable='pathFollower',
        name='pathFollower',
        output='screen',
        parameters=[params_file]
    )
    
    # 地形分析
    terrain_analysis_cmd = Node(
        package='terrain_analysis',
        executable='terrainAnalysis',
        name='terrainAnalysis',
        output='screen',
        parameters=[params_file]
    )
    
    # 扩展地形分析
    terrain_analysis_ext_cmd = Node(
        package='terrain_analysis_ext',
        executable='terrainAnalysisExt',
        name='terrainAnalysisExt',
        output='screen',
        parameters=[params_file]
    )
    
    # LOAM接口
    loam_interface_cmd = Node(
        package='loam_interface',
        executable='loamInterface',
        name='loamInterface',
        output='screen',
        parameters=[params_file]
    )
    
    # 可视化工具
    visualization_tools_cmd = Node(
        package='visualization_tools',
        executable='visualizationTools',
        name='visualizationTools',
        output='screen',
        parameters=[params_file]
    )
    
    # RViz
    rviz_cmd = Node(
        package='rviz2',
        executable='rviz2',
        name='rviz2',
        output='screen',
        arguments=['-d', rviz_config],
        condition=IfCondition(use_rviz)
    )
    
    # 仿真环境启动（可选）
    simulator_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            PathJoinSubstitution([
                FindPackageShare('vehicle_simulator'),
                'launch',
                ['system_', world_file, '.launch']
            ])
        ]),
        condition=IfCondition(use_simulator)
    )
    
    # 自定义生命周期管理器（仅管理非Nav2模块）
    lifecycle_manager_custom_cmd = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_custom',
        output='screen',
        parameters=[
            {'use_sim_time': use_sim_time},
            {'autostart': True},
            {'node_names': [
                'terrainAnalysis',
                'terrainAnalysisExt',
                'localPlanner',
                'pathFollower',
                'loamInterface',
                'visualizationTools'
            ]}
        ]
    )
    
    # 组织启动描述
    ld = LaunchDescription()
    
    # 添加参数声明
    ld.add_action(declare_use_sim_time_cmd)
    ld.add_action(declare_params_file_cmd)
    ld.add_action(declare_use_rviz_cmd)
    ld.add_action(declare_rviz_config_cmd)
    ld.add_action(declare_use_simulator_cmd)
    ld.add_action(declare_world_file_cmd)
    
    # 设置全局参数
    ld.add_action(set_use_sim_time)
    
    # 仿真环境（可选）
    ld.add_action(simulator_launch)
    
    # 核心算法模块（不包含Nav2/SMAC）
    ld.add_action(terrain_analysis_cmd)
    ld.add_action(terrain_analysis_ext_cmd)
    ld.add_action(local_planner_cmd)
    ld.add_action(path_follower_cmd)
    ld.add_action(loam_interface_cmd)
    ld.add_action(visualization_tools_cmd)
    
    # 生命周期管理器
    ld.add_action(lifecycle_manager_custom_cmd)
    
    # 可视化
    ld.add_action(rviz_cmd)
    
    return ld