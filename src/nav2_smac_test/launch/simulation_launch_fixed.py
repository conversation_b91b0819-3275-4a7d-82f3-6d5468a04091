import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction, ExecuteProcess
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from launch_ros.parameter_descriptions import ParameterValue
from launch.substitutions import Command
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    # Get the package directory
    pkg_share = FindPackageShare(package='nav2_smac_test').find('nav2_smac_test')
    
    # Paths to files
    world_path = PathJoinSubstitution([pkg_share, 'worlds', 'test_world.world'])
    urdf_path = PathJoinSubstitution([pkg_share, 'urdf', 'robot.urdf.xacro'])
    #urdf_path = PathJoinSubstitution([pkg_share, 'urdf', 'diff_robot.urdf'])
    map_yaml_path = PathJoinSubstitution([pkg_share, 'maps', 'test_map.yaml'])
    nav2_params_path = PathJoinSubstitution([pkg_share, 'config', 'nav2_params.yaml'])
    rviz_config_path = PathJoinSubstitution([
        FindPackageShare('nav2_bringup'), 'rviz', 'nav2_default_view.rviz'
    ])

    # Launch configuration variables
    use_sim_time = LaunchConfiguration('use_sim_time')
    use_rviz = LaunchConfiguration('use_rviz')
    use_gazebo_gui = LaunchConfiguration('use_gazebo_gui')
    x_pose = LaunchConfiguration('x_pose')
    y_pose = LaunchConfiguration('y_pose')
    yaw_pose = LaunchConfiguration('yaw_pose')
    autostart = LaunchConfiguration('autostart')

    # Declare launch arguments
    declare_use_sim_time_cmd = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation (Gazebo) clock if true')

    declare_use_rviz_cmd = DeclareLaunchArgument(
        'use_rviz',
        default_value='true',
        description='Whether to start RVIZ')

    declare_use_gazebo_gui_cmd = DeclareLaunchArgument(
        'use_gazebo_gui',
        default_value='true',
        description='Whether to start Gazebo GUI')

    declare_x_position_cmd = DeclareLaunchArgument(
        'x_pose',
        default_value='0.0',
        description='Initial x position of the robot')

    declare_y_position_cmd = DeclareLaunchArgument(
        'y_pose',
        default_value='0.0',
        description='Initial y position of the robot')

    declare_yaw_position_cmd = DeclareLaunchArgument(
        'yaw_pose',
        default_value='0.0',
        description='Initial yaw orientation of the robot')

    declare_autostart_cmd = DeclareLaunchArgument(
        'autostart',
        default_value='true',
        description='Automatically startup the nav2 stack')

    # Start Gazebo server
    start_gazebo_server_cmd = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(
            get_package_share_directory('gazebo_ros'), 'launch', 'gzserver.launch.py')),
        launch_arguments={'world': world_path,
                         'verbose': 'false'}.items())

    # Start Gazebo client
    start_gazebo_client_cmd = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(os.path.join(
            get_package_share_directory('gazebo_ros'), 'launch', 'gzclient.launch.py')),
        condition=IfCondition(use_gazebo_gui))

    # Robot State Publisher
    robot_state_publisher_cmd = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{
            'use_sim_time': use_sim_time,
            'robot_description': ParameterValue(Command(['xacro ', urdf_path]), value_type=str)
        }])

    # Spawn robot in Gazebo (delayed to ensure Gazebo is ready)
    spawn_robot_cmd = TimerAction(
        period=3.0,
        actions=[
            Node(
                package='gazebo_ros',
                executable='spawn_entity.py',
                arguments=['-topic', 'robot_description',
                          '-entity', 'test_robot',
                          '-x', x_pose,
                          '-y', y_pose,
                          '-z', '0.01',
                          '-Y', yaw_pose],
                output='screen')
        ])

    # Navigation2 bringup (delayed to ensure robot is spawned)
    nav2_bringup_cmd = TimerAction(
        period=6.0,
        actions=[
            IncludeLaunchDescription(
                PythonLaunchDescriptionSource(os.path.join(
                    get_package_share_directory('nav2_bringup'), 'launch', 'bringup_launch.py')),
                launch_arguments={
                    'map': map_yaml_path,
                    'use_sim_time': use_sim_time,
                    'params_file': nav2_params_path,
                    'autostart': autostart
                }.items())
        ])

    # RVIZ (delayed to ensure navigation is ready)
    rviz_cmd = TimerAction(
        period=8.0,
        actions=[
            Node(
                package='rviz2',
                executable='rviz2',
                name='rviz2',
                arguments=['-d', rviz_config_path],
                parameters=[{'use_sim_time': use_sim_time}],
                output='screen',
                condition=IfCondition(use_rviz))
        ])

    # Create the launch description and populate
    ld = LaunchDescription()

    # Add the commands to the launch description
    ld.add_action(declare_use_sim_time_cmd)
    ld.add_action(declare_use_rviz_cmd)
    ld.add_action(declare_use_gazebo_gui_cmd)
    ld.add_action(declare_x_position_cmd)
    ld.add_action(declare_y_position_cmd)
    ld.add_action(declare_yaw_position_cmd)
    ld.add_action(declare_autostart_cmd)

    ld.add_action(start_gazebo_server_cmd)
    ld.add_action(start_gazebo_client_cmd)
    
    ld.add_action(robot_state_publisher_cmd)
    ld.add_action(spawn_robot_cmd)
    
    ld.add_action(nav2_bringup_cmd)
    
    ld.add_action(rviz_cmd)

    return ld