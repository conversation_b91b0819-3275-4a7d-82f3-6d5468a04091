# 整合导航系统 - 编译和测试指南

## 系统概述

本整合导航系统结合了SMAC全局路径规划器和原有的局部路径规划、避障功能，实现了完整的ROS2导航堆栈。

### 主要特性
- **SMAC全局规划器**: 支持复杂环境下的全局路径规划
- **局部路径规划**: 343条预计算路径的实时局部避障
- **地形分析**: 3D体素化地形分析和可通行性评估
- **统一接口**: 标准Nav2接口兼容

## 目录结构

```
/home/<USER>/saoxueche/
├── src/
│   ├── integrated_navigation/          # 整合导航包
│   │   ├── config/
│   │   │   └── integrated_nav_params.yaml
│   │   ├── launch/
│   │   │   ├── integrated_navigation.launch.py
│   │   │   └── algorithm_only.launch.py
│   │   ├── maps/
│   │   │   ├── test_map.yaml
│   │   │   └── test_map.pgm
│   │   ├── package.xml
│   │   └── CMakeLists.txt
│   ├── local_planner/                  # 局部路径规划器
│   ├── terrain_analysis/               # 地形分析
│   ├── terrain_analysis_ext/           # 扩展地形分析
│   ├── loam_interface/                 # SLAM接口
│   ├── vehicle_simulator/              # 车辆仿真器（可选）
│   ├── visualization_tools/            # 可视化工具
│   └── nav2_smac_test/                # SMAC测试（参考）
├── build/                              # 编译输出
├── install/                            # 安装目录
└── log/                               # 日志文件
```

## 依赖安装

### 1. ROS2基础依赖

```bash
# Ubuntu 22.04 + ROS2 Humble
sudo apt update
sudo apt install -y \
    ros-humble-desktop \
    ros-humble-navigation2 \
    ros-humble-nav2-bringup \
    ros-humble-nav2-smac-planner \
    ros-humble-turtlebot3-msgs \
    ros-humble-gazebo-ros-pkgs
```

### 2. 点云处理依赖

```bash
sudo apt install -y \
    ros-humble-pcl-ros \
    ros-humble-pcl-conversions \
    libpcl-dev \
    libeigen3-dev
```

### 3. 仿真依赖（可选）

```bash
sudo apt install -y \
    ros-humble-gazebo-ros \
    ros-humble-robot-state-publisher \
    ros-humble-xacro
```

## 编译步骤

### 1. 环境准备

```bash
# 设置ROS2环境
source /opt/ros/humble/setup.bash

# 进入工作空间
cd /home/<USER>/saoxueche
```

### 2. 安装rosdep依赖

```bash
# 初始化rosdep（首次使用）
sudo rosdep init
rosdep update

# 安装所有依赖
rosdep install --from-paths src --ignore-src -r -y
```

### 3. 编译工作空间

```bash
# 完整编译
colcon build

# 仅编译核心包（更快）
colcon build --packages-select integrated_navigation local_planner terrain_analysis

# 编译时显示详细信息
colcon build --event-handlers console_direct+
```

### 4. 环境设置

```bash
# 加载编译结果
source install/setup.bash

# 添加到bashrc（可选）
echo "source /home/<USER>/saoxueche/install/setup.bash" >> ~/.bashrc
```

## 运行和测试

### 1. 算法模式启动（推荐用于实际机器人）

```bash
# 启动核心算法（无仿真）
ros2 launch integrated_navigation algorithm_only.launch.py

# 使用自定义地图
ros2 launch integrated_navigation algorithm_only.launch.py map_file:=/path/to/your/map.yaml

# 不启动RViz
ros2 launch integrated_navigation algorithm_only.launch.py use_rviz:=false
```

### 2. 完整仿真模式

```bash
# 启动完整系统（包含仿真）
ros2 launch integrated_navigation integrated_navigation.launch.py use_simulator:=true

# 选择不同环境
ros2 launch integrated_navigation integrated_navigation.launch.py use_simulator:=true world_file:=forest
ros2 launch integrated_navigation integrated_navigation.launch.py use_simulator:=true world_file:=campus
```

### 3. 单独测试组件

```bash
# 测试SMAC规划器
ros2 launch nav2_smac_test smac_planner_test.launch.py

# 测试地形分析
ros2 launch terrain_analysis terrain_analysis.launch

# 测试局部规划器
ros2 launch local_planner local_planner.launch
```

## 系统验证

### 1. 节点状态检查

```bash
# 检查所有节点
ros2 node list

# 应该看到以下核心节点：
# /map_server
# /planner_server
# /global_costmap
# /local_costmap
# /localPlanner
# /terrainAnalysis
# /pathFollower
# /lifecycle_manager_navigation
# /lifecycle_manager_localization
```

### 2. 话题检查

```bash
# 检查关键话题
ros2 topic list

# 核心话题应包括：
# /map                    # 地图数据
# /plan                   # 全局路径
# /path                   # 局部路径
# /cmd_vel                # 速度控制
# /registered_scan        # 点云数据
# /terrain_map           # 地形图
# /state_estimation      # 位姿估计
```

### 3. 性能监控

```bash
# 监控话题频率（应达到性能指标）
ros2 topic hz /terrain_map      # 应该 >2Hz
ros2 topic hz /path            # 应该 >10Hz
ros2 topic hz /plan            # 全局规划频率

# 监控延迟
ros2 topic delay /registered_scan /path    # 应该 <100ms

# 查看资源使用
htop
```

## 参数调优

### 1. SMAC规划器参数

编辑 `src/integrated_navigation/config/integrated_nav_params.yaml`:

```yaml
planner_server:
  ros__parameters:
    GridBased:
      # 调整规划精度与速度平衡
      tolerance: 0.25                    # 减小提高精度
      max_planning_time: 5.0             # 增加获得更好路径
      minimum_turning_radius: 0.30       # 根据机器人调整
      
      # 路径优化
      analytic_expansion_ratio: 4.5      # 增加获得更平滑路径
      smooth_path: True                  # 启用路径平滑
```

### 2. 局部规划器参数

```yaml
localPlanner:
  ros__parameters:
    # 障碍物检测敏感度
    obstacleHeightThre: 0.2             # 根据环境调整
    adjacentRange: 3.5                  # 感知范围
    
    # 路径选择权重
    dirWeight: 0.02                     # 方向权重
    pathScale: 1.0                      # 路径缩放
```

### 3. 地形分析参数

```yaml
terrainAnalysis:
  ros__parameters:
    # 分辨率设置
    scanVoxelSize: 0.05                 # 体素大小
    terrainVoxelSize: 0.2               # 地形网格大小
    
    # 时间衰减
    decayTime: 2.0                      # 数据保持时间
    vehicleHeight: 1.5                  # 车辆通过高度
```

## 故障排查

### 1. 编译问题

```bash
# 清理重编译
rm -rf build install log
colcon build

# 检查依赖
rosdep check --from-paths src --ignore-src

# 单独编译问题包
colcon build --packages-select <package_name> --event-handlers console_direct+
```

### 2. 运行时问题

```bash
# 检查TF树
ros2 run tf2_tools view_frames

# 查看参数
ros2 param list /localPlanner
ros2 param get /planner_server GridBased.tolerance

# 查看日志
ros2 log get_logs
```

### 3. 常见问题

| 问题 | 症状 | 解决方案 |
|------|------|----------|
| 路径规划失败 | 无/plan话题输出 | 检查地图加载，调整tolerance |
| 地形图不更新 | /terrain_map无数据 | 检查/registered_scan话题 |
| 路径震荡 | 机器人左右摆动 | 调整dirWeight和dirThre参数 |
| 计算延迟 | 响应慢 | 优化scanVoxelSize参数 |

## 测试用例

### 1. 基础功能测试

```bash
# 测试1：地图加载
ros2 service call /map_server/load_map nav2_msgs/srv/LoadMap "{map_url: '/path/to/map.yaml'}"

# 测试2：全局规划
ros2 action send_goal /compute_path_to_pose nav2_msgs/action/ComputePathToPose "
{
  goal: {
    pose: {
      position: {x: 2.0, y: 1.0, z: 0.0},
      orientation: {x: 0.0, y: 0.0, z: 0.0, w: 1.0}
    }
  }
}"

# 测试3：设置导航目标
ros2 topic pub --once /goal_pose geometry_msgs/msg/PoseStamped "
{
  header: {
    frame_id: 'map'
  },
  pose: {
    position: {x: 2.0, y: 1.0, z: 0.0},
    orientation: {x: 0.0, y: 0.0, z: 0.0, w: 1.0}
  }
}"
```

### 2. 性能测试

```bash
# 启动性能监控脚本
#!/bin/bash
echo "开始性能测试..."

# 监控5分钟
timeout 300 ros2 topic hz /terrain_map &
timeout 300 ros2 topic hz /path &
timeout 300 ros2 topic delay /registered_scan /path &

wait
echo "性能测试完成"
```

## 部署建议

### 1. 实际机器人部署

- 使用 `algorithm_only.launch.py`
- 设置 `use_sim_time:=false`
- 确保SLAM系统正常运行
- 调整传感器话题映射

### 2. 优化配置

- 根据机器人尺寸调整 `minimum_turning_radius`
- 根据计算能力调整体素分辨率
- 根据环境复杂度调整规划时间限制

### 3. 监控和维护

- 定期检查节点状态
- 监控内存和CPU使用率
- 备份调优后的参数文件

## 扩展和定制

### 1. 添加新传感器

在配置文件中添加新的observation_sources：

```yaml
local_costmap:
  local_costmap:
    ros__parameters:
      voxel_layer:
        observation_sources: scan pointcloud camera
        camera:
          topic: /camera/depth/points
          data_type: "PointCloud2"
          # ... 其他参数
```

### 2. 自定义规划器

可以通过插件机制添加自定义规划算法，或修改现有算法参数。

### 3. 集成其他SLAM系统

修改loam_interface或创建新的接口包来支持不同的SLAM系统。

---

## 技术支持

如遇到问题，请检查：
1. ROS2版本兼容性（推荐Humble）
2. 依赖包完整性
3. 参数配置正确性
4. 硬件性能要求

更多信息请参考Nav2官方文档和各组件的README文件。

基于现在的情况，我看到SMAC规划器和Nav2组件都正确安装了，但lifecycle管理存在问题。让我总结一下当前的整合状态并给出测试建议：

  🎯 SMAC规划器集成测试总结

  ✅ 已完成的整合工作

  1. 代码结构整合 - ✅ 完成
    - 统一的/home/<USER>/saoxueche/src/目录结构
    - integrated_navigation包成功创建
    - 所有原有模块保留并放置在正确位置
  2. 配置文件整合 - ✅ 完成
    - integrated_nav_params.yaml - 统一配置文件
    - 包含SMAC全局规划器参数
    - 包含局部规划器和地形分析参数
    - 修复了pathFolder参数缺失问题
  3. Launch文件创建 - ✅ 完成
    - integrated_navigation.launch.py - 完整系统
    - algorithm_only.launch.py - 算法模式
    - test_smac_simple.launch.py - SMAC测试
  4. RViz配置 - ✅ 完成
    - 专用的integrated_navigation.rviz配置文件
    - 包含所有关键显示组件

  🔍 当前测试状态

  SMAC规划器组件状态:
  - ✅ nav2_smac_planner包已安装
  - ✅ nav2_planner服务器可以启动
  - ⚠️ Lifecycle管理器等待服务响应（常见的启动时序问题）

  检测到的问题:
  1. LocalPlanner路径文件 - ✅ 已修复（添加pathFolder参数）
  2. VisualizationTools崩溃 - ⚠️ 需要进一步调试
  3. Lifecycle启动时序 - ⚠️ Nav2系统常见问题

  📋 建议的测试步骤

  由于lifecycle管理存在时序问题，建议使用以下分步测试方法：

  # 1. 基础测试：验证包和依赖
  source install/setup.bash
  ros2 pkg list | grep -E "(nav2_smac|integrated)"

  # 2. 单独测试地图服务器
  ros2 run nav2_map_server map_server --ros-args -p yaml_filename:=/home/<USER>/saoxueche/src/integrated_navigation/maps/test_map.yaml &

  # 3. 单独测试SMAC规划器（在另一个终端）
  ros2 run nav2_planner planner_server --ros-args --params-file /home/<USER>/saoxueche/src/integrated_navigation/config/integrated_nav_params.yaml &

  # 4. 测试路径规划功能
  ros2 action send_goal /compute_path_to_pose nav2_msgs/action/ComputePathToPose "{goal: {pose: {position: {x: 2.0, y: 1.0, z: 0.0}}}}"

 

不启动smac全局路径规划算法的测试：
启动命令：
  # 基础启动（不含仿真）
  ros2 launch integrated_navigation no_smac_test.launch.py

  # 包含仿真环境
  ros2 launch integrated_navigation no_smac_test.launch.py use_simulator:=true

  # 不启动RViz
  ros2 launch integrated_navigation no_smac_test.launch.py use_rviz:=false

  包含的模块：
  - local_planner（局部路径规划）
  - path_follower（路径跟踪）
  - terrain_analysis（地形分析）
  - terrain_analysis_ext（扩展地形分析）
  - loam_interface（LOAM接口）
  - visualization_tools（可视化工具）

  排除的nav2_smac模块：
  - planner_server（SMAC全局规划器）
  - global_costmap/local_costmap（代价地图）
  - map_server（地图服务器）