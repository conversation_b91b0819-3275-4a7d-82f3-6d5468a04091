#!/usr/bin/env python3

import os
import sys
import argparse
from pathlib import Path
import subprocess

def extract_bag_topics(input_bag_path, output_bag_path, topics_to_extract):
    """
    从ROS2 bag中提取指定话题并保存为新的bag文件
    
    Args:
        input_bag_path (str): 输入bag文件路径
        output_bag_path (str): 输出bag文件路径
        topics_to_extract (list): 需要提取的话题列表
    """
    
    # 检查输入bag文件是否存在
    if not os.path.exists(input_bag_path):
        print(f"错误: 输入bag文件不存在: {input_bag_path}")
        return False
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_bag_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
    
    # 构建ros2 bag record命令
    cmd = [
        'ros2', 'bag', 'play', input_bag_path, '--clock'
    ]
    
    # 构建ros2 bag record命令
    record_cmd = [
        'ros2', 'bag', 'record',
        '-o', output_bag_path
    ] + topics_to_extract
    
    print(f"从 {input_bag_path} 提取话题: {', '.join(topics_to_extract)}")
    print(f"保存到: {output_bag_path}")
    
    try:
        # 启动bag播放（后台进程）
        print("启动bag播放...")
        play_process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待一秒让播放进程启动
        import time
        time.sleep(1)
        
        # 启动bag录制
        print("开始录制指定话题...")
        record_process = subprocess.run(record_cmd, capture_output=True, text=True, timeout=None)
        
        # 结束播放进程
        play_process.terminate()
        play_process.wait()
        
        if record_process.returncode == 0:
            print(f"成功提取话题到: {output_bag_path}")
            return True
        else:
            print(f"录制失败: {record_process.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("录制超时")
        return False
    except Exception as e:
        print(f"提取过程中发生错误: {e}")
        return False

def list_bag_topics(bag_path):
    """
    列出bag文件中的所有话题
    
    Args:
        bag_path (str): bag文件路径
    
    Returns:
        list: 话题列表
    """
    try:
        result = subprocess.run(
            ['ros2', 'bag', 'info', bag_path],
            capture_output=True, text=True, check=True
        )
        
        lines = result.stdout.split('\n')
        topics = []
        in_topic_section = False
        
        for line in lines:
            if 'Topic information:' in line:
                in_topic_section = True
                continue
            elif in_topic_section and line.strip():
                if line.startswith('Topic:'):
                    topic_name = line.split('Topic:')[1].split('|')[0].strip()
                    topics.append(topic_name)
        
        return topics
        
    except subprocess.CalledProcessError as e:
        print(f"无法获取bag信息: {e}")
        return []

def extract_with_filter(input_bag_path, output_bag_path, topic_patterns):
    """
    使用ros2 bag的内置过滤功能提取话题（推荐方法）
    
    Args:
        input_bag_path (str): 输入bag文件路径
        output_bag_path (str): 输出bag文件路径  
        topic_patterns (list): 话题名称或模式列表
    """
    
    # 检查输入bag文件是否存在
    if not os.path.exists(input_bag_path):
        print(f"错误: 输入bag文件不存在: {input_bag_path}")
        return False
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_bag_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
    
    # 构建过滤命令
    cmd = [
        'ros2', 'bag', 'convert',
        '-i', input_bag_path,
        '-o', output_bag_path
    ]
    
    # 添加话题过滤
    for topic in topic_patterns:
        cmd.extend(['--include', topic])
    
    print(f"从 {input_bag_path} 提取话题: {', '.join(topic_patterns)}")
    print(f"保存到: {output_bag_path}")
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(f"成功提取话题到: {output_bag_path}")
        print("提取完成!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"提取失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"提取过程中发生错误: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(
        description='从ROS2 bag中提取指定话题并保存为新的bag文件',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用示例:
  # 提取指定话题
  python3 extract_bag_topics.py input.bag output.bag -t /topic1 /topic2 /topic3
  
  # 列出bag中的所有话题
  python3 extract_bag_topics.py input.bag --list
  
  # 使用通配符模式提取话题
  python3 extract_bag_topics.py input.bag output.bag -t "/camera/*" "/lidar/*"
  
  # 提取导航相关话题
  python3 extract_bag_topics.py input.bag nav_only.bag -t /cmd_vel /odom /scan /path
        '''
    )
    
    parser.add_argument('input_bag', help='输入bag文件路径')
    parser.add_argument('output_bag', nargs='?', help='输出bag文件路径')
    parser.add_argument('-t', '--topics', nargs='+', 
                       help='需要提取的话题列表')
    parser.add_argument('--list', action='store_true',
                       help='列出输入bag中的所有话题')
    parser.add_argument('--method', choices=['convert', 'record'], default='convert',
                       help='提取方法: convert(推荐,快速) 或 record(慢,需要播放)')
    
    # 预定义的话题组合
    parser.add_argument('--nav-only', action='store_true',
                       help='仅提取导航相关话题')
    parser.add_argument('--sensor-only', action='store_true', 
                       help='仅提取传感器数据话题')
    parser.add_argument('--control-only', action='store_true',
                       help='仅提取控制相关话题')
    
    args = parser.parse_args()
    
    # 列出话题模式
    if args.list:
        print(f"正在分析bag文件: {args.input_bag}")
        topics = list_bag_topics(args.input_bag)
        if topics:
            print(f"\n找到 {len(topics)} 个话题:")
            for i, topic in enumerate(topics, 1):
                print(f"  {i:2d}. {topic}")
        else:
            print("未找到话题或无法解析bag文件")
        return
    
    # 检查必需参数
    if not args.output_bag:
        print("错误: 需要指定输出bag文件路径")
        parser.print_help()
        return
    
    # 确定要提取的话题
    topics_to_extract = []
    
    if args.nav_only:
        topics_to_extract.extend([
            '/cmd_vel', '/odom', '/scan', '/path', '/plan',
            '/state_estimation', '/way_point', '/goal_pose',
            '/map', '/costmap', '/local_costmap'
        ])
        print("使用导航话题预设")
        
    elif args.sensor_only:
        topics_to_extract.extend([
            '/scan', '/camera/image_raw', '/camera/camera_info',
            '/imu/data', '/registered_scan', '/velodyne_points',
            '/points_raw', '/cloud_registered'
        ])
        print("使用传感器话题预设")
        
    elif args.control_only:
        topics_to_extract.extend([
            '/cmd_vel', '/joy', '/speed', '/stop',
            '/state_estimation', '/odom', '/imu'
        ])
        print("使用控制话题预设")
    
    # 添加用户指定的话题
    if args.topics:
        topics_to_extract.extend(args.topics)
    
    if not topics_to_extract:
        print("错误: 没有指定要提取的话题")
        parser.print_help()
        return
    
    # 去重
    topics_to_extract = list(set(topics_to_extract))
    
    # 执行提取
    success = False
    if args.method == 'convert':
        success = extract_with_filter(args.input_bag, args.output_bag, topics_to_extract)
    else:
        success = extract_bag_topics(args.input_bag, args.output_bag, topics_to_extract)
    
    if success:
        print("\n提取完成! 可以使用以下命令播放:")
        print(f"ros2 bag play {args.output_bag}")
    else:
        print("提取失败!")
        sys.exit(1)

if __name__ == '__main__':
    main()