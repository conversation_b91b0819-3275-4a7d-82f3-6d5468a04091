问题分析：90度方向偏差的根本原因

  通过分析代码，我发现了90度偏差的根本原因：

  问题定位

  1. send_path.py 发送 PointStamped 消息（只有位置，无方向）
  2. localPlanner.cpp 接收航点后计算目标方向：

  // src/local_planner/src/localPlanner.cpp:606-609
  float relativeGoalX = ((goalX - vehicleX) * cosVehicleYaw + (goalY - vehicleY) * sinVehicleYaw);
  float relativeGoalY = (-(goalX - vehicleX) * sinVehicleYaw + (goalY - vehicleY) * cosVehicleYaw);
  joyDir = atan2(relativeGoalY, relativeGoalX) * 180 / PI;

  核心问题

  坐标系变换中的负号问题：relativeGoalY 计算中的负号表明使用了非标准ROS坐标系定义。

  - 标准ROS坐标系：X轴向前，Y轴向左
  - 这里的实现：Y轴定义可能相差90度

  解决方案

  方案1：修正坐标变换（推荐）

  // 移除relativeGoalY计算中的负号
  float relativeGoalY = ((goalX - vehicleX) * sinVehicleYaw + (goalY - vehicleY) * cosVehicleYaw);

  方案2：角度补偿

  joyDir = atan2(relativeGoalY, relativeGoalX) * 180 / PI + 90.0; // 添加90度补偿

  方案3：使用Pose消息

  修改send_path发送 PoseStamped 而不是 PointStamped，直接包含方向信息。

  建议先尝试方案1，这是最直接的修复方法。
