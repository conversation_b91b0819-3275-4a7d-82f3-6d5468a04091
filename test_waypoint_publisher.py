#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PointStamped

class WaypointPublisher(Node):
    def __init__(self):
        super().__init__('test_waypoint_publisher')
        self.publisher = self.create_publisher(PointStamped, '/way_point', 10)
        self.timer = self.create_timer(5.0, self.publish_waypoint)  # 每5秒发布一次
        self.get_logger().info('Test waypoint publisher started')
        self.waypoint_count = 0

    def publish_waypoint(self):
        waypoint_msg = PointStamped()
        waypoint_msg.header.stamp = self.get_clock().now().to_msg()
        waypoint_msg.header.frame_id = "vehicle"  # 使用vehicle坐标系
        
        # 发布不同的目标点
        waypoints = [
            (5.0, 0.0, 0.0),    # 前方5米
            (3.0, 2.0, 0.0),    # 右前方
            (3.0, -2.0, 0.0),   # 左前方
            (2.0, 0.0, 0.0),    # 前方2米
        ]
        
        current_waypoint = waypoints[self.waypoint_count % len(waypoints)]
        waypoint_msg.point.x = current_waypoint[0]
        waypoint_msg.point.y = current_waypoint[1]
        waypoint_msg.point.z = current_waypoint[2]
        
        self.publisher.publish(waypoint_msg)
        self.get_logger().info(f'Published waypoint {self.waypoint_count + 1}: ({current_waypoint[0]}, {current_waypoint[1]}, {current_waypoint[2]})')
        
        self.waypoint_count += 1

def main(args=None):
    rclpy.init(args=args)
    node = WaypointPublisher()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    
    node.destroy_node()
    rclpy.shutdown()

if __name__ == '__main__':
    main()
