#!/usr/bin/env python3

import yaml
import sqlite3
import sys
import os

def create_metadata_for_extracted_bag(bag_db_path, metadata_path):
    """为提取的bag文件创建正确的metadata.yaml"""
    
    try:
        # 连接数据库获取话题信息
        conn = sqlite3.connect(bag_db_path)
        cursor = conn.cursor()
        
        # 获取话题信息
        cursor.execute("SELECT name, type FROM topics")
        topics = cursor.fetchall()
        
        # 获取消息统计
        cursor.execute("SELECT COUNT(*) FROM messages")
        total_messages = cursor.fetchone()[0]
        
        # 获取时间范围
        cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM messages")
        time_result = cursor.fetchone()
        start_time = time_result[0] if time_result[0] else 0
        end_time = time_result[1] if time_result[1] else 0
        
        conn.close()
        
        # 创建metadata结构
        metadata = {
            'rosbag2_bagfile_information': {
                'version': 4,
                'storage_identifier': 'sqlite3',
                'relative_file_paths': [os.path.basename(bag_db_path)],
                'duration': {
                    'nanoseconds': end_time - start_time
                },
                'starting_time': {
                    'nanoseconds_since_epoch': start_time
                },
                'message_count': total_messages,
                'topics_with_message_count': []
            }
        }
        
        # 添加话题信息
        for topic_name, topic_type in topics:
            # 获取每个话题的消息数
            conn = sqlite3.connect(bag_db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM messages WHERE topic_id = (SELECT id FROM topics WHERE name = ?)", (topic_name,))
            message_count = cursor.fetchone()[0]
            conn.close()
            
            topic_info = {
                'topic_metadata': {
                    'name': topic_name,
                    'type': topic_type,
                    'serialization_format': 'cdr'
                },
                'message_count': message_count
            }
            metadata['rosbag2_bagfile_information']['topics_with_message_count'].append(topic_info)
        
        # 写入metadata文件
        with open(metadata_path, 'w') as f:
            yaml.dump(metadata, f, default_flow_style=False)
        
        print(f"已创建metadata文件: {metadata_path}")
        print(f"包含 {len(topics)} 个话题，总计 {total_messages} 条消息")
        
    except Exception as e:
        print(f"创建metadata时发生错误: {e}")

if __name__ == '__main__':
    if len(sys.argv) != 3:
        print("用法: python3 create_metadata.py <bag_db_file> <metadata_output_path>")
        sys.exit(1)
    
    create_metadata_for_extracted_bag(sys.argv[1], sys.argv[2])
