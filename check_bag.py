#!/usr/bin/env python3

import sqlite3
import sys

def check_bag_content(bag_db_path):
    """检查bag数据库的内容"""
    try:
        conn = sqlite3.connect(bag_db_path)
        cursor = conn.cursor()
        
        # 检查话题
        cursor.execute("SELECT id, name, type FROM topics")
        topics = cursor.fetchall()
        print("话题信息:")
        for topic in topics:
            topic_id, name, topic_type = topic
            
            # 统计每个话题的消息数量
            cursor.execute("SELECT COUNT(*) FROM messages WHERE topic_id = ?", (topic_id,))
            count = cursor.fetchone()[0]
            
            print(f"  ID: {topic_id}, 话题: {name}, 类型: {topic_type}, 消息数: {count}")
        
        # 总消息数
        cursor.execute("SELECT COUNT(*) FROM messages")
        total_messages = cursor.fetchone()[0]
        print(f"\n总消息数: {total_messages}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时发生错误: {e}")

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("用法: python3 check_bag.py <bag_db_file>")
        sys.exit(1)
    
    check_bag_content(sys.argv[1])
