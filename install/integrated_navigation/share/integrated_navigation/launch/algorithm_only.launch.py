#!/usr/bin/env python3

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node, SetParameter
from launch_ros.substitutions import FindPackageShare
from launch.conditions import IfCondition


def generate_launch_description():
    # 包目录
    integrated_nav_pkg = FindPackageShare('integrated_navigation')
    
    # Launch配置变量
    use_sim_time = LaunchConfiguration('use_sim_time')
    map_file = LaunchConfiguration('map_file')
    params_file = LaunchConfiguration('params_file')
    use_rviz = LaunchConfiguration('use_rviz')
    
    # 默认路径
    default_params_file = PathJoinSubstitution([
        integrated_nav_pkg, 'config', 'integrated_nav_params.yaml'
    ])
    default_map_file = PathJoinSubstitution([
        integrated_nav_pkg, 'maps', 'test_map.yaml'
    ])
    default_rviz_config = PathJoinSubstitution([
        integrated_nav_pkg, 'config', 'integrated_navigation.rviz'
    ])
    
    # Launch参数声明
    declare_use_sim_time_cmd = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='Use simulation time if true'
    )
    
    declare_map_file_cmd = DeclareLaunchArgument(
        'map_file',
        default_value=default_map_file,
        description='Full path to map yaml file'
    )
    
    declare_params_file_cmd = DeclareLaunchArgument(
        'params_file',
        default_value=default_params_file,
        description='Full path to navigation parameters file'
    )
    
    declare_use_rviz_cmd = DeclareLaunchArgument(
        'use_rviz',
        default_value='true',
        description='Whether to start RViz'
    )
    
    # 设置全局参数
    set_use_sim_time = SetParameter('use_sim_time', use_sim_time)
    
    # 地图服务器
    map_server_cmd = Node(
        package='nav2_map_server',
        executable='map_server',
        name='map_server',
        output='screen',
        parameters=[
            params_file,
            {'use_sim_time': use_sim_time},
            {'yaml_filename': map_file}
        ]
    )
    
    # SMAC全局规划器
    planner_server_cmd = Node(
        package='nav2_planner',
        executable='planner_server',
        name='planner_server',
        output='screen',
        parameters=[params_file]
    )
    
    # 全局代价地图
    global_costmap_cmd = Node(
        package='nav2_costmap_2d',
        executable='nav2_costmap_2d',
        name='global_costmap',
        namespace='global_costmap',
        output='screen',
        parameters=[params_file]
    )
    
    # 局部代价地图
    local_costmap_cmd = Node(
        package='nav2_costmap_2d',
        executable='nav2_costmap_2d',
        name='local_costmap',
        namespace='local_costmap',
        output='screen',
        parameters=[params_file]
    )
    
    # 本地路径规划器（保留原有功能）
    local_planner_cmd = Node(
        package='local_planner',
        executable='localPlanner',
        name='localPlanner',
        output='screen',
        parameters=[params_file],
        remappings=[
            ('/path', '/plan'),  # 接收SMAC规划器的全局路径
            ('/move_base/cmd_vel', '/cmd_vel')  # 输出速度控制指令
        ]
    )
    
    # 路径跟踪器
    path_follower_cmd = Node(
        package='local_planner',
        executable='pathFollower',
        name='pathFollower',
        output='screen',
        parameters=[params_file]
    )
    
    # 地形分析
    terrain_analysis_cmd = Node(
        package='terrain_analysis',
        executable='terrainAnalysis',
        name='terrainAnalysis',
        output='screen',
        parameters=[params_file]
    )
    
    # LOAM接口（用于实际机器人）
    loam_interface_cmd = Node(
        package='loam_interface',
        executable='loamInterface',
        name='loamInterface',
        output='screen',
        parameters=[params_file]
    )
    
    # 可视化工具（临时禁用以避免字符串错误）
    # visualization_tools_cmd = Node(
    #     package='visualization_tools',
    #     executable='visualizationTools',
    #     name='visualizationTools',
    #     output='screen',
    #     parameters=[params_file]
    # )
    
    # RViz（可选）
    rviz_cmd = Node(
        package='rviz2',
        executable='rviz2',
        name='rviz2',
        output='screen',
        arguments=['-d', default_rviz_config],
        condition=IfCondition(use_rviz)
    )
    
    # 核心导航生命周期管理器
    lifecycle_manager_navigation_cmd = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_navigation',
        output='screen',
        parameters=[
            {'use_sim_time': use_sim_time},
            {'autostart': True},
            {'node_names': [
                'map_server',
                'planner_server',
                'global_costmap',
                'local_costmap'
            ]}
        ]
    )
    
    # 局部规划器生命周期管理器
    lifecycle_manager_localization_cmd = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_localization',
        output='screen',
        parameters=[
            {'use_sim_time': use_sim_time},
            {'autostart': True},
            {'node_names': [
                'terrainAnalysis',
                'localPlanner',
                'pathFollower',
                'loamInterface'
            ]}
        ]
    )
    
    # 组织启动描述
    ld = LaunchDescription()
    
    # 添加参数声明
    ld.add_action(declare_use_sim_time_cmd)
    ld.add_action(declare_map_file_cmd)
    ld.add_action(declare_params_file_cmd)
    ld.add_action(declare_use_rviz_cmd)
    
    # 设置全局参数
    ld.add_action(set_use_sim_time)
    
    # 核心导航节点
    ld.add_action(map_server_cmd)
    ld.add_action(planner_server_cmd)
    ld.add_action(global_costmap_cmd)
    ld.add_action(local_costmap_cmd)
    
    # 本地规划和地形分析节点
    ld.add_action(terrain_analysis_cmd)
    ld.add_action(local_planner_cmd)
    ld.add_action(path_follower_cmd)
    ld.add_action(loam_interface_cmd)
    # ld.add_action(visualization_tools_cmd)  # 临时禁用
    
    # 生命周期管理器
    ld.add_action(lifecycle_manager_navigation_cmd)
    ld.add_action(lifecycle_manager_localization_cmd)
    
    # 可视化
    ld.add_action(rviz_cmd)
    
    return ld