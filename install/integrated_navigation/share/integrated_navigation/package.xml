<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>integrated_navigation</name>
  <version>1.0.0</version>
  <description>Integrated navigation system combining SMAC global planner with local path planning and obstacle avoidance</description>
  <maintainer email="<EMAIL>">User</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <!-- Navigation2 dependencies -->
  <depend>nav2_bringup</depend>
  <depend>nav2_common</depend>
  <depend>nav2_smac_planner</depend>
  <depend>nav2_planner</depend>
  <depend>nav2_controller</depend>
  <depend>nav2_costmap_2d</depend>
  <depend>nav2_lifecycle_manager</depend>
  <depend>nav2_map_server</depend>
  <depend>nav2_amcl</depend>
  <depend>nav2_bt_navigator</depend>
  <depend>navigation2</depend>
  
  <!-- Simulation and robot dependencies -->
  <depend>gazebo_ros_pkgs</depend>
  <depend>robot_state_publisher</depend>
  <depend>xacro</depend>
  
  <!-- Core ROS2 dependencies -->
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>std_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>rclcpp</depend>
  <depend>rclpy</depend>

  <!-- Local planner dependencies -->
  <depend>pcl_ros</depend>
  <depend>pcl_conversions</depend>
  <depend>message_filters</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>